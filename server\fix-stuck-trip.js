const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function fixStuckTrip() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Fixing Stuck Trip 174\n');
    
    // Check current state of Trip 174
    const trip174 = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_number,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time, tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.assignment_code, a.loading_location_id, a.unloading_location_id,
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.id = 174
    `);
    
    if (trip174.rows.length === 0) {
      console.log('❌ Trip 174 not found');
      return;
    }
    
    const trip = trip174.rows[0];
    console.log('📋 Current state of Trip 174:');
    console.log(`  Status: ${trip.status}`);
    console.log(`  Route: ${trip.loading_name} → ${trip.unloading_name}`);
    console.log(`  Times: L(${trip.loading_start_time ? 'Y' : 'N'}-${trip.loading_end_time ? 'Y' : 'N'}) U(${trip.unloading_start_time ? 'Y' : 'N'}-${trip.unloading_end_time ? 'Y' : 'N'}) C(${trip.trip_completed_time ? 'Y' : 'N'})`);
    
    if (trip.status === 'unloading_end') {
      console.log('\n🔧 Completing Trip 174...');
      
      // Complete the trip properly
      await client.query(`
        UPDATE trip_logs
        SET 
          status = 'trip_completed',
          trip_completed_time = CURRENT_TIMESTAMP,
          actual_unloading_location_id = $1,
          total_duration_minutes = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = 174
      `, [trip.unloading_location_id]);
      
      console.log('  ✅ Trip 174 completed successfully');
      
      // Verify the completion
      const completedTrip = await client.query(`
        SELECT id, status, trip_completed_time, actual_unloading_location_id
        FROM trip_logs WHERE id = 174
      `);
      
      const completed = completedTrip.rows[0];
      console.log(`  Status: ${completed.status}`);
      console.log(`  Completion Time: ${completed.trip_completed_time}`);
      console.log(`  Actual Unloading ID: ${completed.actual_unloading_location_id}`);
      
    } else {
      console.log(`\n✅ Trip 174 is already in status: ${trip.status}`);
    }
    
    // Now check if we can test the A→B→C workflow
    console.log('\n🧪 Testing A→B→C Workflow Setup...');
    
    // Check for recent completed trips
    const recentTrips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.status = 'trip_completed'
        AND tl.trip_completed_time > NOW() - INTERVAL '1 hour'
      ORDER BY tl.trip_completed_time DESC
    `);
    
    console.log('Recent completed trips for DT-100:');
    recentTrips.rows.forEach((trip, index) => {
      console.log(`  ${index + 1}. Trip ${trip.id}: ${trip.loading_name} → ${trip.unloading_name} (${trip.trip_completed_time})`);
    });
    
    // Check for active trips
    const activeTrips = await client.query(`
      SELECT 
        tl.id, tl.status, 
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.status NOT IN ('trip_completed', 'cancelled')
    `);
    
    console.log('\nActive trips for DT-100:');
    if (activeTrips.rows.length > 0) {
      activeTrips.rows.forEach(trip => {
        console.log(`  Trip ${trip.id}: ${trip.status} | ${trip.loading_name} → ${trip.unloading_name}`);
      });
    } else {
      console.log('  ✅ No active trips - ready for new workflow!');
    }
    
    if (recentTrips.rows.length > 0 && activeTrips.rows.length === 0) {
      console.log('\n🎉 System is ready for A→B→C workflow testing!');
      console.log('  You can now scan DT-100 at Point A to start a new trip');
      console.log('  The post-completion detection should work correctly');
    }
    
    console.log('\n🔧 Fix Complete!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

fixStuckTrip().catch(console.error);
