const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function checkTripNotes() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking Trip Notes Structure...');

    // Get Trip #2 notes
    const trip2Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.notes as trip_notes,
        pg_typeof(tl.notes) as notes_type
      FROM trip_logs tl
      WHERE tl.id = 104
    `);

    if (trip2Result.rows.length === 0) {
      console.log('❌ Trip #2 not found');
      return;
    }

    const trip2 = trip2Result.rows[0];
    console.log(`\n📊 Trip #2 Notes Analysis:`);
    console.log(`Trip ID: ${trip2.id}`);
    console.log(`Status: ${trip2.status}`);
    console.log(`Notes Type: ${trip2.notes_type}`);
    console.log(`Raw Notes:`, trip2.trip_notes);

    if (trip2.trip_notes) {
      try {
        let notes;
        if (typeof trip2.trip_notes === 'string') {
          notes = JSON.parse(trip2.trip_notes);
        } else {
          notes = trip2.trip_notes;
        }
        
        console.log('\n📋 Parsed Notes:');
        console.log(JSON.stringify(notes, null, 2));
        
        if (notes.completion_method) {
          console.log(`\n✅ Completion Method Found: ${notes.completion_method}`);
          const isAutoCompleted = notes.completion_method.includes('auto_completed');
          console.log(`Is Auto-Completed: ${isAutoCompleted ? 'Yes' : 'No'}`);
        } else {
          console.log('\n❌ No completion_method found in notes');
        }
        
      } catch (e) {
        console.log(`\n❌ Failed to parse notes: ${e.message}`);
      }
    } else {
      console.log('\n❌ No notes found');
    }

    // Check all recent trips to understand the pattern
    console.log('\n🧪 Checking all recent trips for auto-completion patterns...');
    const allTrips = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.notes as trip_notes
      FROM trip_logs tl
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.trip_number DESC, tl.created_at DESC
    `);

    allTrips.rows.forEach((trip, index) => {
      console.log(`\n🚚 Trip #${trip.trip_number} (ID: ${trip.id}):`);
      console.log(`   Status: ${trip.status}`);
      
      const hasLoading = !!trip.loading_start_time && !!trip.loading_end_time;
      const hasUnloading = !!trip.unloading_start_time && !!trip.unloading_end_time;
      
      console.log(`   Loading Phase: ${hasLoading ? 'Complete' : 'Incomplete'}`);
      console.log(`   Unloading Phase: ${hasUnloading ? 'Complete' : 'Incomplete'}`);
      
      if (trip.trip_notes) {
        try {
          let notes;
          if (typeof trip.trip_notes === 'string') {
            notes = JSON.parse(trip.trip_notes);
          } else {
            notes = trip.trip_notes;
          }
          
          if (notes.completion_method) {
            console.log(`   Completion Method: ${notes.completion_method}`);
            const isAutoCompleted = notes.completion_method.includes('auto_completed');
            
            if (isAutoCompleted && trip.status === 'trip_completed') {
              if (hasLoading && !hasUnloading) {
                console.log(`   ✅ Should display: "Loading Completed (Auto-completed)"`);
              } else if (hasUnloading) {
                console.log(`   ✅ Should display: "Completed (Auto-completed)"`);
              } else {
                console.log(`   ✅ Should display: "Auto-completed"`);
              }
            } else {
              console.log(`   ✅ Should display: Normal status`);
            }
          } else {
            console.log(`   Notes: No completion method`);
          }
        } catch (e) {
          console.log(`   Notes: Parse error - ${e.message}`);
        }
      } else {
        console.log(`   Notes: None`);
      }
    });

  } catch (error) {
    console.error('❌ Error checking trip notes:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

checkTripNotes().catch(console.error);
