const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testFixVerification() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Testing A→B→C Fix Verification\n');
    
    // Step 1: Create a proper completed trip with actual location data
    console.log('📋 Step 1: Creating a properly completed A→B trip...');
    
    // Get truck and locations
    const truck = await client.query(`
      SELECT id, truck_number, license_plate 
      FROM dump_trucks 
      WHERE status = 'active' 
      LIMIT 1
    `);
    
    const locations = await client.query(`
      SELECT id, name, type, location_code
      FROM locations 
      WHERE type IN ('loading', 'unloading')
      ORDER BY type, name
      LIMIT 10
    `);
    
    const loadingLocations = locations.rows.filter(l => l.type === 'loading');
    const unloadingLocations = locations.rows.filter(l => l.type === 'unloading');
    
    if (truck.rows.length === 0 || loadingLocations.length < 2 || unloadingLocations.length < 1) {
      console.log('❌ Insufficient test data');
      return;
    }
    
    const truckData = truck.rows[0];
    const locationA = loadingLocations[0];  // Original loading
    const locationB = unloadingLocations[0]; // Unloading
    const locationC = loadingLocations[1];  // New loading for extension
    
    console.log(`  Truck: ${truckData.truck_number}`);
    console.log(`  Location A (loading): ${locationA.name}`);
    console.log(`  Location B (unloading): ${locationB.name}`);
    console.log(`  Location C (loading): ${locationC.name}`);
    
    // Clean up any existing test data
    await client.query(`
      DELETE FROM trip_logs
      WHERE assignment_id IN (
        SELECT id FROM assignments
        WHERE truck_id = $1
          AND (assignment_code LIKE 'TEST-FIX-%' OR assignment_code LIKE 'TEST-EXT-%')
      )
    `, [truckData.id]);

    await client.query(`
      DELETE FROM assignments
      WHERE truck_id = $1
        AND (assignment_code LIKE 'TEST-FIX-%' OR assignment_code LIKE 'TEST-EXT-%')
    `, [truckData.id]);

    // Also clean up any existing assignments for these exact locations to avoid duplicates
    await client.query(`
      DELETE FROM assignments
      WHERE truck_id = $1
        AND loading_location_id = $2
        AND unloading_location_id = $3
        AND assigned_date = CURRENT_DATE
    `, [truckData.id, locationA.id, locationB.id]);
    
    // Create assignment A→B
    const assignment = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
        assigned_date, status, priority, expected_loads_per_day, notes
      )
      SELECT 
        'TEST-FIX-' || EXTRACT(EPOCH FROM NOW())::text,
        $1, 
        (SELECT id FROM drivers LIMIT 1),
        $2, $3,
        CURRENT_DATE, 'assigned', 'normal', 1,
        '{"test_scenario": "fix_verification"}'
      RETURNING *
    `, [truckData.id, locationA.id, locationB.id]);
    
    const assignmentData = assignment.rows[0];
    console.log(`  Created assignment: ${assignmentData.assignment_code}`);
    
    // Create and properly complete A→B trip with actual location data
    const completedTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, 
        loading_start_time, loading_end_time, 
        unloading_start_time, unloading_end_time, trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        workflow_type, location_sequence, total_duration_minutes
      )
      VALUES (
        $1, 1, 'trip_completed',
        NOW() - INTERVAL '2 hours',
        NOW() - INTERVAL '1 hour 45 minutes',
        NOW() - INTERVAL '1 hour 30 minutes', 
        NOW() - INTERVAL '1 hour 15 minutes',
        NOW() - INTERVAL '1 hour',
        $2, $3,
        'standard',
        $4,
        60
      )
      RETURNING *
    `, [
      assignmentData.id, 
      locationA.id, 
      locationB.id,
      JSON.stringify([
        { name: locationA.name, type: 'loading', confirmed: true, location_id: locationA.id },
        { name: locationB.name, type: 'unloading', confirmed: true, location_id: locationB.id }
      ])
    ]);
    
    const baselineTrip = completedTrip.rows[0];
    console.log(`  ✅ Created properly completed A→B trip: ${baselineTrip.id}`);
    console.log(`    Status: ${baselineTrip.status}`);
    console.log(`    Actual Loading ID: ${baselineTrip.actual_loading_location_id}`);
    console.log(`    Actual Unloading ID: ${baselineTrip.actual_unloading_location_id}`);
    
    // Step 2: Test the checkRecentCompletedTrip logic
    console.log('\n📋 Step 2: Testing post-completion detection...');
    
    const recentCompletedTrips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        ll.name as loading_location_name, ul.name as unloading_location_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON tl.actual_loading_location_id = ll.id
      LEFT JOIN locations ul ON tl.actual_unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.status = 'trip_completed'
        AND tl.trip_completed_time > NOW() - INTERVAL '30 minutes'
      ORDER BY tl.trip_completed_time DESC
      LIMIT 1
    `, [truckData.id]);
    
    if (recentCompletedTrips.rows.length > 0) {
      const recentTrip = recentCompletedTrips.rows[0];
      console.log(`  ✅ Found recent completed trip: ${recentTrip.id}`);
      console.log(`    Route: ${recentTrip.loading_location_name} → ${recentTrip.unloading_location_name}`);
      console.log(`    Actual Loading ID: ${recentTrip.actual_loading_location_id}`);
      console.log(`    Actual Unloading ID: ${recentTrip.actual_unloading_location_id}`);
      
      // Test workflow type determination
      let workflowType = 'none';
      if (locationC.id !== recentTrip.actual_loading_location_id &&
          locationC.id !== recentTrip.actual_unloading_location_id) {
        workflowType = 'extended'; // A→B→C extension
      } else if (locationC.id === recentTrip.actual_unloading_location_id) {
        workflowType = 'cycle'; // C→B→C cycle
      }
      
      console.log(`    Workflow type for ${locationC.name}: ${workflowType}`);
      
      if (workflowType === 'extended' && recentTrip.actual_unloading_location_id) {
        console.log(`\n✅ CRITICAL FIX VERIFIED:`);
        console.log(`  - Baseline trip has proper actual_unloading_location_id: ${recentTrip.actual_unloading_location_id}`);
        console.log(`  - Extended workflow can now be created: ${locationC.name} → ${recentTrip.unloading_location_name}`);
        console.log(`  - The A→B→C workflow should work correctly now!`);
        
        // Test creating the extended workflow
        console.log(`\n📋 Step 3: Testing extended workflow creation...`);
        
        try {
          // Create new assignment for C→B
          const extendedAssignment = await client.query(`
            INSERT INTO assignments (
              assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
              assigned_date, status, priority, expected_loads_per_day, notes
            )
            VALUES (
              'TEST-EXT-' || EXTRACT(EPOCH FROM NOW())::text,
              $1, $2, $3, $4,
              CURRENT_DATE, 'assigned', 'normal', 1,
              $5
            )
            RETURNING *
          `, [
            truckData.id,
            assignmentData.driver_id,
            locationC.id,
            recentTrip.actual_unloading_location_id,
            JSON.stringify({
              creation_method: 'extended_workflow',
              baseline_trip_id: recentTrip.id,
              workflow_type: 'extended'
            })
          ]);
          
          console.log(`    ✅ Extended assignment created: ${extendedAssignment.rows[0].assignment_code}`);
          
          // Create extended trip
          const extendedTrip = await client.query(`
            INSERT INTO trip_logs (
              assignment_id, trip_number, status, 
              is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
              location_sequence
            )
            VALUES ($1, 1, 'loading_start', true, 'extended', $2, 1, $3)
            RETURNING *
          `, [
            extendedAssignment.rows[0].id,
            recentTrip.id,
            JSON.stringify([
              { name: locationC.name, type: 'loading', confirmed: true, location_id: locationC.id },
              { name: recentTrip.unloading_location_name, type: 'unloading', confirmed: false, location_id: recentTrip.actual_unloading_location_id }
            ])
          ]);
          
          console.log(`    ✅ Extended trip created: ${extendedTrip.rows[0].id}`);
          console.log(`    ✅ A→B→C workflow creation successful!`);
          
        } catch (error) {
          console.log(`    ❌ Extended workflow creation failed: ${error.message}`);
        }
        
      } else {
        console.log(`\n❌ ISSUE STILL EXISTS:`);
        console.log(`  - Workflow type: ${workflowType}`);
        console.log(`  - Actual unloading ID: ${recentTrip.actual_unloading_location_id}`);
        console.log(`  - The fix may not be complete`);
      }
      
    } else {
      console.log(`  ❌ No recent completed trips found`);
    }
    
    console.log('\n🎉 Fix Verification Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testFixVerification().catch(console.error);
