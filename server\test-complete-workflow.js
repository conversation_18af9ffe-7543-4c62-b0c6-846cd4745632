const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testCompleteWorkflow() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Complete A→B→C Workflow Test\n');
    
    // Step 1: Verify current state
    console.log('📋 Step 1: Verifying current state...');
    
    const currentState = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        ll.name as loading_name, ul.name as unloading_name,
        tl.actual_loading_location_id, tl.actual_unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.created_at > NOW() - INTERVAL '2 hours'
      ORDER BY tl.created_at DESC
    `);
    
    console.log('Recent trips for DT-100:');
    currentState.rows.forEach((trip, index) => {
      console.log(`  ${index + 1}. Trip ${trip.id}: ${trip.status} | ${trip.loading_name} → ${trip.unloading_name}`);
      console.log(`     Actual IDs: ${trip.actual_loading_location_id} → ${trip.actual_unloading_location_id}`);
    });
    
    // Step 2: Test the enhanced checkRecentCompletedTrip function
    console.log('\n📋 Step 2: Testing enhanced post-completion detection...');
    
    const truckId = 1; // DT-100
    const locationId = 4; // POINT C - LOADING
    
    // Use the enhanced query from the scanner
    const recentTrip = await client.query(`
      SELECT
        tl.*,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        -- Calculate workflow potential
        CASE
          WHEN $2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id) 
           AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'extended'
          WHEN $2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'cycle'
          ELSE 'none'
        END as workflow_potential
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.status = 'trip_completed'
        AND tl.trip_completed_time > (CURRENT_TIMESTAMP - INTERVAL '1 hour')
        AND (
          -- Find trips that can be extended (location is different from both loading/unloading)
          ($2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id) 
           AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id))
          OR
          -- Or find trips that can be cycled (location matches unloading)
          ($2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id))
        )
      ORDER BY 
        -- Prioritize extended workflows over cycles
        CASE WHEN (CASE
          WHEN $2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id) 
           AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'extended'
          WHEN $2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'cycle'
          ELSE 'none'
        END) = 'extended' THEN 1 ELSE 2 END,
        tl.trip_completed_time DESC
      LIMIT 1
    `, [truckId, locationId]);
    
    if (recentTrip.rows.length > 0) {
      const trip = recentTrip.rows[0];
      console.log(`  ✅ Found baseline trip: ${trip.id} (${trip.workflow_potential})`);
      console.log(`    Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
      console.log(`    Actual IDs: ${trip.actual_loading_location_id} → ${trip.actual_unloading_location_id}`);
      
      if (trip.workflow_potential === 'extended') {
        console.log('\n🔄 Should create extended workflow:');
        console.log(`    Baseline: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
        console.log(`    Extension: POINT C - LOADING → ${trip.unloading_location_name}`);
        
        // Step 3: Simulate the extended workflow creation
        console.log('\n📋 Step 3: Simulating extended workflow creation...');
        
        // Get the unloading location for the extension
        const unloadingLocationId = trip.actual_unloading_location_id || trip.unloading_location_id;
        
        if (unloadingLocationId) {
          console.log(`    Creating C→B assignment (${locationId} → ${unloadingLocationId})...`);
          
          // Create extended assignment
          const extendedAssignment = await client.query(`
            INSERT INTO assignments (
              assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
              assigned_date, status, priority, expected_loads_per_day, notes
            )
            VALUES (
              'EXT-TEST-' || EXTRACT(EPOCH FROM NOW())::text,
              $1, 
              (SELECT driver_id FROM assignments WHERE id = $2),
              $3, $4,
              CURRENT_DATE, 'assigned', 'normal', 1,
              $5
            )
            RETURNING *
          `, [
            truckId,
            trip.assignment_id,
            locationId,
            unloadingLocationId,
            JSON.stringify({
              creation_method: 'extended_workflow',
              baseline_trip_id: trip.id,
              workflow_type: 'extended'
            })
          ]);
          
          const assignment = extendedAssignment.rows[0];
          console.log(`    ✅ Created extended assignment: ${assignment.assignment_code}`);
          
          // Create extended trip
          const extendedTrip = await client.query(`
            INSERT INTO trip_logs (
              assignment_id, trip_number, status, 
              is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
              location_sequence
            )
            VALUES ($1, 1, 'loading_start', true, 'extended', $2, 1, $3)
            RETURNING *
          `, [
            assignment.id,
            trip.id,
            JSON.stringify([
              { name: 'POINT C - LOADING', type: 'loading', confirmed: true, location_id: locationId },
              { name: trip.unloading_location_name, type: 'unloading', confirmed: false, location_id: unloadingLocationId }
            ])
          ]);
          
          const newTrip = extendedTrip.rows[0];
          console.log(`    ✅ Created extended trip: ${newTrip.id}`);
          
          // Mark baseline trip as extended
          await client.query(`
            UPDATE trip_logs
            SET notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb
            WHERE id = $2
          `, [
            JSON.stringify({
              extended_workflow: true,
              extended_at: new Date().toISOString(),
              extended_trip_id: newTrip.id,
              workflow_continuation: 'extended_to_next_location'
            }),
            trip.id
          ]);
          
          console.log(`    ✅ Marked baseline trip ${trip.id} as extended`);
          
          // Step 4: Verify the workflow
          console.log('\n📋 Step 4: Verifying extended workflow...');
          
          const verification = await client.query(`
            SELECT 
              tl.id, tl.status, tl.workflow_type, tl.is_extended_trip, tl.baseline_trip_id,
              ll.name as loading_name, ul.name as unloading_name,
              a.assignment_code,
              CASE 
                WHEN tl.notes::text LIKE '%extended_workflow%' THEN 'Has Extended Notes'
                ELSE 'No Extended Notes'
              END as notes_status
            FROM trip_logs tl
            JOIN assignments a ON tl.assignment_id = a.id
            LEFT JOIN locations ll ON a.loading_location_id = ll.id
            LEFT JOIN locations ul ON a.unloading_location_id = ul.id
            WHERE a.truck_id = $1
              AND tl.created_at > NOW() - INTERVAL '10 minutes'
            ORDER BY tl.created_at DESC
          `, [truckId]);
          
          console.log('Recent workflow trips:');
          verification.rows.forEach((t, index) => {
            const indicators = [];
            if (t.is_extended_trip) indicators.push('🔄 Extended');
            if (t.baseline_trip_id) indicators.push(`📎 Baseline: ${t.baseline_trip_id}`);
            if (t.notes_status === 'Has Extended Notes') indicators.push('📝 Extended Notes');
            
            console.log(`  ${index + 1}. Trip ${t.id}: ${t.status} | ${t.workflow_type} | ${t.loading_name} → ${t.unloading_name} | ${indicators.join(', ')}`);
          });
          
          console.log('\n🎉 Extended Workflow Test Complete!');
          console.log('✅ The A→B→C workflow has been successfully created');
          console.log('✅ Now the truck can scan at Point A to start a new cycle');
          
        } else {
          console.log('    ❌ Cannot create extension - no unloading location found');
        }
        
      } else {
        console.log(`\n❌ Workflow potential is ${trip.workflow_potential}, not extended`);
      }
      
    } else {
      console.log('  ❌ No suitable baseline trip found for extension');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testCompleteWorkflow().catch(console.error);
