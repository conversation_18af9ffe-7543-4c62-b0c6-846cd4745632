const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testEnhancedQuery() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Testing Enhanced checkRecentCompletedTrip Query\n');
    
    const truckId = 1; // DT-100
    const locationId = 4; // POINT C - LOADING
    
    console.log(`📋 Testing with truck ID: ${truckId}, location ID: ${locationId}`);
    
    // Test the enhanced query
    const result = await client.query(`
      SELECT
        tl.*,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        -- Calculate workflow potential
        CASE
          WHEN $2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id) 
           AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'extended'
          WHEN $2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'cycle'
          ELSE 'none'
        END as workflow_potential
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.status = 'trip_completed'
        AND tl.trip_completed_time > (CURRENT_TIMESTAMP - INTERVAL '1 hour')
        AND (
          -- Find trips that can be extended (location is different from both loading/unloading)
          ($2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id) 
           AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id))
          OR
          -- Or find trips that can be cycled (location matches unloading)
          ($2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id))
        )
      ORDER BY
        -- Prioritize extended workflows over cycles
        CASE WHEN (CASE
          WHEN $2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id)
           AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'extended'
          WHEN $2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'cycle'
          ELSE 'none'
        END) = 'extended' THEN 1 ELSE 2 END,
        tl.trip_completed_time DESC
    `, [truckId, locationId]);
    
    console.log(`\n📊 Query Results (${result.rows.length} trips found):`);
    
    result.rows.forEach((trip, index) => {
      console.log(`\n${index + 1}. Trip ${trip.id}:`);
      console.log(`   Status: ${trip.status}`);
      console.log(`   Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
      console.log(`   Actual Loading ID: ${trip.actual_loading_location_id}`);
      console.log(`   Actual Unloading ID: ${trip.actual_unloading_location_id}`);
      console.log(`   Assignment Loading ID: ${trip.loading_location_id}`);
      console.log(`   Assignment Unloading ID: ${trip.unloading_location_id}`);
      console.log(`   Workflow Potential: ${trip.workflow_potential}`);
      console.log(`   Completed: ${trip.trip_completed_time}`);
    });
    
    if (result.rows.length > 0) {
      const selectedTrip = result.rows[0];
      console.log(`\n✅ Selected Trip: ${selectedTrip.id} (${selectedTrip.workflow_potential})`);
      
      if (selectedTrip.workflow_potential === 'extended') {
        console.log(`🔄 This should create an extended workflow:`);
        console.log(`   Baseline: ${selectedTrip.loading_location_name} → ${selectedTrip.unloading_location_name}`);
        console.log(`   Extension: POINT C - LOADING → ${selectedTrip.unloading_location_name}`);
      } else if (selectedTrip.workflow_potential === 'cycle') {
        console.log(`🔄 This should create a cycle workflow:`);
        console.log(`   Previous: ${selectedTrip.loading_location_name} → ${selectedTrip.unloading_location_name}`);
        console.log(`   Cycle: POINT C - LOADING → ${selectedTrip.unloading_location_name}`);
      }
    } else {
      console.log('\n❌ No trips found that can be extended or cycled');
      
      // Show all recent completed trips for debugging
      console.log('\n🔍 All recent completed trips:');
      const allTrips = await client.query(`
        SELECT
          tl.id, tl.status, tl.trip_completed_time,
          ll.name as loading_location_name,
          ul.name as unloading_location_name,
          tl.actual_loading_location_id,
          tl.actual_unloading_location_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE a.truck_id = $1
          AND tl.status = 'trip_completed'
          AND tl.trip_completed_time > (CURRENT_TIMESTAMP - INTERVAL '1 hour')
        ORDER BY tl.trip_completed_time DESC
      `, [truckId]);
      
      allTrips.rows.forEach((trip, index) => {
        console.log(`  ${index + 1}. Trip ${trip.id}: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
        console.log(`     Actual IDs: ${trip.actual_loading_location_id} → ${trip.actual_unloading_location_id}`);
        console.log(`     Completed: ${trip.trip_completed_time}`);
      });
    }
    
    console.log('\n🎉 Enhanced Query Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testEnhancedQuery().catch(console.error);
