#!/usr/bin/env node

/**
 * Test Frontend Parsing Logic
 * 
 * This script simulates the frontend parsing logic to identify
 * why the multi-location workflow might not be displaying.
 */

// Sample trip data from the database debug output
const sampleTrips = [
  {
    id: 142,
    status: 'trip_completed',
    is_extended_trip: false,
    workflow_type: 'standard',
    location_sequence: [
      {
        "name": "POINT C - LOADING",
        "type": "loading",
        "confirmed": true,
        "location_id": 4
      },
      {
        "name": "Point B - Primary Dump Site",
        "type": "unloading",
        "confirmed": true,
        "location_id": 2
      }
    ],
    assignment_notes: null
  },
  {
    id: 141,
    status: 'trip_completed',
    is_extended_trip: false,
    workflow_type: 'standard',
    location_sequence: [
      {
        "name": "Point A - Main Loading Site",
        "type": "loading",
        "confirmed": true,
        "location_id": 1
      },
      {
        "name": "Point B - Primary Dump Site",
        "type": "unloading",
        "confirmed": true,
        "location_id": 2
      }
    ],
    assignment_notes: null
  }
];

// Simulate the frontend parsing logic from TripsTable.js
function simulateRenderEnhancedRoute(trip) {
  console.log(`\n🔍 Testing trip ${trip.id}:`);
  
  // Debug logging for troubleshooting
  if (trip.id && (trip.location_sequence || trip.is_extended_trip)) {
    console.log('🔍 DEBUG - Trip', trip.id, ':', {
      has_location_sequence: !!trip.location_sequence,
      location_sequence_type: typeof trip.location_sequence,
      location_sequence_value: trip.location_sequence,
      is_extended_trip: trip.is_extended_trip,
      workflow_type: trip.workflow_type
    });
  }

  // Check for multi-location workflow
  const isExtendedTrip = trip.is_extended_trip || false;
  const workflowType = trip.workflow_type || 'standard';

  // Safely parse location sequence with comprehensive error handling
  let locationSequence = null;
  if (trip.location_sequence) {
    try {
      // Handle both string and object cases
      if (typeof trip.location_sequence === 'string') {
        const trimmed = trip.location_sequence.trim();
        
        // Check for common problematic values
        if (trimmed === '[object Object]' || trimmed === '[object Array]' || trimmed === 'undefined' || trimmed === 'null') {
          console.warn('Detected problematic location_sequence value:', trimmed);
          locationSequence = null;
        } else if (trimmed && (trimmed.startsWith('[') || trimmed.startsWith('{'))) {
          // Only parse if it's a non-empty string that looks like valid JSON
          locationSequence = JSON.parse(trimmed);
        }
      } else if (typeof trip.location_sequence === 'object') {
        if (Array.isArray(trip.location_sequence)) {
          locationSequence = trip.location_sequence;
        } else if (trip.location_sequence !== null) {
          // Handle case where it's an object but not an array
          console.warn('location_sequence is an object but not an array:', trip.location_sequence);
          locationSequence = null;
        }
      }
    } catch (error) {
      console.warn('❌ Failed to parse location_sequence for trip', trip.id, ':', error);
      console.warn('Raw location_sequence value:', trip.location_sequence);
      console.warn('Type:', typeof trip.location_sequence);
      locationSequence = null;
    }
  }

  // Debug the final parsed result
  if (trip.id && trip.location_sequence) {
    console.log('🔍 Parsed location sequence for trip', trip.id, ':', {
      original: trip.location_sequence,
      parsed: locationSequence,
      isArray: Array.isArray(locationSequence),
      length: locationSequence?.length
    });
  }

  // Check if this is a dynamic assignment with safe parsing
  let isDynamicAssignment = false;
  if (trip.assignment_notes) {
    try {
      const notes = typeof trip.assignment_notes === 'string' 
        ? JSON.parse(trip.assignment_notes) 
        : trip.assignment_notes;
      isDynamicAssignment = notes && notes.creation_method === 'dynamic_assignment';
    } catch (error) {
      console.warn('Failed to parse assignment_notes:', error);
      isDynamicAssignment = false;
    }
  }

  // Use location sequence if available and valid, otherwise fall back to traditional display
  if (locationSequence && Array.isArray(locationSequence) && locationSequence.length > 0) {
    // Additional validation: ensure all items in the array are valid location objects
    const isValidLocationSequence = locationSequence.every(loc => 
      loc && typeof loc === 'object' && loc.name && loc.type
    );
    
    if (isValidLocationSequence) {
      console.log('✅ Should render multi-location route for trip', trip.id);
      console.log('   Route:', locationSequence.map(loc => `${loc.name} (${loc.type})`).join(' → '));
      return 'MULTI_LOCATION_ROUTE';
    } else {
      console.log('❌ Invalid location sequence structure for trip', trip.id, ':', locationSequence);
    }
  }

  // Fall back to traditional route display for standard trips
  console.log('📍 Should render traditional route for trip', trip.id);
  return 'TRADITIONAL_ROUTE';
}

function runTest() {
  console.log('🧪 Testing Frontend Multi-Location Parsing Logic...\n');

  sampleTrips.forEach(trip => {
    const result = simulateRenderEnhancedRoute(trip);
    console.log(`   Result: ${result}`);
  });

  console.log('\n📊 Test Summary:');
  console.log('   - Both trips have valid location_sequence data');
  console.log('   - Both trips should render multi-location routes');
  console.log('   - If they\'re not showing in the UI, the issue might be:');
  console.log('     1. API not returning the data correctly');
  console.log('     2. Frontend component not being called');
  console.log('     3. CSS/styling hiding the content');
  console.log('     4. React rendering issue');
}

// Run the test
if (require.main === module) {
  runTest();
}

module.exports = { simulateRenderEnhancedRoute, sampleTrips };
