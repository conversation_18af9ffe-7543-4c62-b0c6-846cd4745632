const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function checkLocationSequences() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking Location Sequence Confirmation Indicators...\n');
    
    const result = await client.query(`
      SELECT 
        id, status, workflow_type,
        location_sequence
      FROM trip_logs 
      WHERE location_sequence IS NOT NULL
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    result.rows.forEach(trip => {
      console.log(`Trip ${trip.id} (${trip.workflow_type}):`);
      const sequence = trip.location_sequence;
      if (Array.isArray(sequence)) {
        sequence.forEach((loc, index) => {
          const indicator = loc.confirmed ? '📍' : '❓';
          const typeIcon = loc.type === 'loading' ? '⬆️' : '⬇️';
          console.log(`  ${indicator} ${loc.name} ${typeIcon} (confirmed: ${loc.confirmed})`);
        });
      } else {
        console.log(`  Invalid sequence format: ${typeof sequence}`);
      }
      console.log('');
    });

    // Check if we need to create some test data with predicted locations
    console.log('📋 Creating test data with predicted locations...\n');
    
    // Get an assignment for testing
    const assignment = await client.query(`
      SELECT a.*, ll.name as loading_name, ul.name as unloading_name
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      ORDER BY a.created_at DESC
      LIMIT 1
    `);

    const assignmentData = assignment.rows[0];
    
    // Get next trip number
    const nextTrip = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs WHERE assignment_id = $1
    `, [assignmentData.id]);

    const tripNumber = nextTrip.rows[0].next_trip_number;

    // Create a trip with mixed confirmed/predicted locations
    const mixedTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        loading_start_time, loading_end_time,
        actual_loading_location_id, actual_unloading_location_id,
        is_extended_trip, workflow_type,
        location_sequence,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [
      assignmentData.id,
      tripNumber,
      'unloading_start', // In progress
      new Date(Date.now() - 1800000), // 30 minutes ago
      new Date(Date.now() - 1500000), // 25 minutes ago
      assignmentData.loading_location_id,
      assignmentData.unloading_location_id,
      false,
      'standard',
      JSON.stringify([
        {
          name: assignmentData.loading_name,
          type: 'loading',
          confirmed: true, // 📍 Already completed loading
          location_id: assignmentData.loading_location_id
        },
        {
          name: assignmentData.unloading_name,
          type: 'unloading',
          confirmed: false, // ❓ Still in progress/predicted
          location_id: assignmentData.unloading_location_id
        }
      ]),
      new Date(Date.now() - 1800000),
      new Date()
    ]);

    console.log(`✅ Created test trip ${mixedTrip.rows[0].id} with mixed indicators:`);
    console.log(`   📍 ${assignmentData.loading_name} ⬆️ (confirmed - loading completed)`);
    console.log(`   ❓ ${assignmentData.unloading_name} ⬇️ (predicted - unloading in progress)`);

    // Create another trip that's just assigned (all predicted)
    const nextTrip2 = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs WHERE assignment_id = $1
    `, [assignmentData.id]);

    const tripNumber2 = nextTrip2.rows[0].next_trip_number;

    const predictedTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        actual_loading_location_id, actual_unloading_location_id,
        is_extended_trip, workflow_type,
        location_sequence,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      assignmentData.id,
      tripNumber2,
      'assigned', // Not started yet
      assignmentData.loading_location_id,
      assignmentData.unloading_location_id,
      false,
      'standard',
      JSON.stringify([
        {
          name: assignmentData.loading_name,
          type: 'loading',
          confirmed: false, // ❓ Not started yet
          location_id: assignmentData.loading_location_id
        },
        {
          name: assignmentData.unloading_name,
          type: 'unloading',
          confirmed: false, // ❓ Not started yet
          location_id: assignmentData.unloading_location_id
        }
      ]),
      new Date(),
      new Date()
    ]);

    console.log(`✅ Created test trip ${predictedTrip.rows[0].id} with all predicted:`);
    console.log(`   ❓ ${assignmentData.loading_name} ⬆️ (predicted - not started)`);
    console.log(`   ❓ ${assignmentData.unloading_name} ⬇️ (predicted - not started)`);

    console.log('\n🎯 Location Confirmation Indicators Test Summary:');
    console.log('   📍 = Confirmed locations (physically visited/completed)');
    console.log('   ❓ = Predicted locations (not yet visited/in progress)');
    console.log('   ⬆️ = Loading location');
    console.log('   ⬇️ = Unloading location');
    console.log('\n✅ Test data created for frontend validation!');

  } finally {
    client.release();
    await pool.end();
  }
}

checkLocationSequences().catch(console.error);
