#!/usr/bin/env node

/**
 * Dynamic Route Labeling Database Analysis
 * Investigates why established assignments are showing "🔄 Dynamic Route" indicators
 */

const { Pool } = require('./server/node_modules/pg');
require('./server/node_modules/dotenv').config({ path: './server/.env' });

// Database configuration
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
});

// Replicate the current isActiveDiscovery logic from TripsTable.js
function testIsActiveDiscovery(trip) {
  console.log(`\n🧪 Testing isActiveDiscovery logic for Trip ${trip.id}:`);
  
  // Check if assignment is dynamic (auto-created)
  let assignmentNotes = {};
  try {
    assignmentNotes = trip.assignment_notes ? JSON.parse(trip.assignment_notes) : {};
  } catch (e) {
    console.log(`   ⚠️  Failed to parse assignment notes: ${trip.assignment_notes}`);
  }
  
  const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment' && 
                             assignmentNotes.auto_created === true;
  
  console.log(`   Assignment Notes: ${JSON.stringify(assignmentNotes)}`);
  console.log(`   Is Dynamic Assignment: ${isDynamicAssignment}`);
  
  if (!isDynamicAssignment) {
    console.log(`   ✅ RESULT: Normal route display (not dynamic assignment)`);
    return false;
  }
  
  // Check assignment age (recently created = < 30 minutes)
  const assignmentAge = trip.assignment_created_at ? 
    (Date.now() - new Date(trip.assignment_created_at).getTime()) / (1000 * 60) : null;
  const isRecentlyCreated = assignmentAge !== null && assignmentAge < 30;
  
  console.log(`   Assignment Created: ${trip.assignment_created_at}`);
  console.log(`   Assignment Age: ${assignmentAge ? Math.round(assignmentAge) : 'unknown'} minutes`);
  console.log(`   Recently Created (<30 min): ${isRecentlyCreated}`);
  
  // Check if route discovery is complete
  const hasConfirmedLoadingLocation = trip.actual_loading_location_id && 
    ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status);
  const hasConfirmedUnloadingLocation = trip.actual_unloading_location_id && 
    ['unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status);
  
  const routeDiscoveryComplete = hasConfirmedLoadingLocation && hasConfirmedUnloadingLocation;
  
  console.log(`   Actual Loading Location: ${trip.actual_loading_location_id}`);
  console.log(`   Actual Unloading Location: ${trip.actual_unloading_location_id}`);
  console.log(`   Trip Status: ${trip.status}`);
  console.log(`   Has Confirmed Loading: ${hasConfirmedLoadingLocation}`);
  console.log(`   Has Confirmed Unloading: ${hasConfirmedUnloadingLocation}`);
  console.log(`   Route Discovery Complete: ${routeDiscoveryComplete}`);
  
  // Current logic: Show dynamic route if recently created OR route discovery not complete
  const showDynamicRoute = isRecentlyCreated || !routeDiscoveryComplete;
  
  console.log(`   📊 CURRENT LOGIC RESULT: ${showDynamicRoute ? '🔄 Dynamic Route' : 'Normal Route'}`);
  
  return showDynamicRoute;
}

async function analyzeDatabase() {
  console.log('🔍 Dynamic Route Labeling Database Analysis');
  console.log('============================================================\n');
  
  try {
    // Test database connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log(`✅ Connected to database at ${testResult.rows[0].current_time}\n`);
    
    // Query trips with assignment data (matching the trips API query)
    console.log('📋 Querying trips with assignment data...');
    const tripsQuery = `
      SELECT 
        tl.id,
        tl.trip_number,
        tl.status,
        tl.actual_loading_location_id,
        tl.actual_unloading_location_id,
        tl.created_at as trip_created_at,
        a.id as assignment_id,
        a.loading_location_id,
        a.unloading_location_id,
        a.notes as assignment_notes,
        a.created_at as assignment_created_at,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        dt.truck_number,
        d.full_name as driver_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
      ORDER BY tl.created_at DESC
      LIMIT 10
    `;
    
    const result = await pool.query(tripsQuery);
    console.log(`✅ Found ${result.rows.length} recent trips\n`);
    
    if (result.rows.length === 0) {
      console.log('❌ No trips found in the last 7 days');
      return;
    }
    
    // Analyze each trip
    console.log('🔬 DETAILED TRIP ANALYSIS');
    console.log('============================================================');
    
    let problematicTrips = [];
    
    for (const trip of result.rows) {
      console.log(`\n📋 TRIP ${trip.id} - ${trip.truck_number} (${trip.driver_name})`);
      console.log(`   Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
      console.log(`   Status: ${trip.status}`);
      console.log(`   Trip Created: ${trip.trip_created_at}`);
      
      // Test current logic
      const showsDynamicRoute = testIsActiveDiscovery(trip);
      
      // Check if this assignment exists in Assignment Management
      const assignmentExistsQuery = `
        SELECT id, loading_location_id, unloading_location_id, status
        FROM assignments 
        WHERE id = $1 AND loading_location_id IS NOT NULL AND unloading_location_id IS NOT NULL
      `;
      const assignmentResult = await pool.query(assignmentExistsQuery, [trip.assignment_id]);
      const existsInAssignmentManagement = assignmentResult.rows.length > 0;
      
      console.log(`   🏢 Exists in Assignment Management: ${existsInAssignmentManagement}`);
      
      // Identify problematic cases
      if (showsDynamicRoute && existsInAssignmentManagement) {
        console.log(`   ❌ PROBLEM: Shows Dynamic Route but exists in Assignment Management!`);
        problematicTrips.push({
          trip_id: trip.id,
          truck_number: trip.truck_number,
          assignment_id: trip.assignment_id,
          status: trip.status,
          assignment_age_minutes: trip.assignment_created_at ? 
            Math.round((Date.now() - new Date(trip.assignment_created_at).getTime()) / (1000 * 60)) : null,
          assignment_notes: trip.assignment_notes,
          loading_location_id: trip.loading_location_id,
          unloading_location_id: trip.unloading_location_id,
          actual_loading_location_id: trip.actual_loading_location_id,
          actual_unloading_location_id: trip.actual_unloading_location_id
        });
      } else if (showsDynamicRoute) {
        console.log(`   ✅ OK: Shows Dynamic Route and assignment is genuinely new/uncertain`);
      } else {
        console.log(`   ✅ OK: Shows normal route format`);
      }
    }
    
    // Summary of problematic trips
    console.log('\n\n🎯 ANALYSIS SUMMARY');
    console.log('============================================================');
    
    if (problematicTrips.length === 0) {
      console.log('✅ No problematic trips found - all Dynamic Route indicators are correct!');
    } else {
      console.log(`❌ Found ${problematicTrips.length} problematic trip(s):`);
      console.log('\nProblematic Trips (showing Dynamic Route but should show normal route):');
      
      problematicTrips.forEach(trip => {
        console.log(`\n📋 Trip ${trip.trip_id} (${trip.truck_number}):`);
        console.log(`   Assignment ID: ${trip.assignment_id}`);
        console.log(`   Status: ${trip.status}`);
        console.log(`   Assignment Age: ${trip.assignment_age_minutes} minutes`);
        console.log(`   Assignment Notes: ${trip.assignment_notes}`);
        console.log(`   Planned Route: ${trip.loading_location_id} → ${trip.unloading_location_id}`);
        console.log(`   Actual Locations: ${trip.actual_loading_location_id} → ${trip.actual_unloading_location_id}`);
      });
      
      console.log('\n🔧 ROOT CAUSE ANALYSIS:');
      console.log('The current logic shows Dynamic Route when:');
      console.log('1. Assignment is recently created (<30 min) OR');
      console.log('2. Route discovery is not complete (both locations not physically confirmed)');
      console.log('\n❌ PROBLEM: This logic doesn\'t account for assignments that:');
      console.log('- Exist in Assignment Management with confirmed loading_location_id and unloading_location_id');
      console.log('- Are established assignments regardless of their original creation method');
      console.log('- Should NEVER show Dynamic Route indicators once they appear in Assignment Management');
    }
    
  } catch (error) {
    console.error('❌ Database analysis failed:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the analysis
analyzeDatabase().catch(console.error);
