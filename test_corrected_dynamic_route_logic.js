#!/usr/bin/env node

/**
 * Test Corrected Dynamic Route Logic
 * Tests the new logic that checks Assignment Management visibility
 */

const { Pool } = require('./server/node_modules/pg');
require('./server/node_modules/dotenv').config({ path: './server/.env' });

// Database configuration
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
});

// CORRECTED isActiveDiscovery logic
function testCorrectedIsActiveDiscovery(trip) {
  console.log(`\n🧪 Testing CORRECTED isActiveDiscovery logic for Trip ${trip.id}:`);
  
  // Check if assignment is dynamic (auto-created)
  let assignmentNotes = {};
  try {
    assignmentNotes = trip.assignment_notes ? JSON.parse(trip.assignment_notes) : {};
  } catch (e) {
    console.log(`   ⚠️  Failed to parse assignment notes: ${trip.assignment_notes}`);
  }
  
  const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment' && 
                             assignmentNotes.auto_created === true;
  
  console.log(`   Assignment Notes: ${JSON.stringify(assignmentNotes)}`);
  console.log(`   Is Dynamic Assignment: ${isDynamicAssignment}`);
  
  if (!isDynamicAssignment) {
    console.log(`   ✅ RESULT: Normal route display (not dynamic assignment)`);
    return false;
  }
  
  // NEW LOGIC: Check if assignment exists in Assignment Management with established locations
  const hasEstablishedLoadingLocation = trip.loading_location_id !== null && trip.loading_location_id !== undefined;
  const hasEstablishedUnloadingLocation = trip.unloading_location_id !== null && trip.unloading_location_id !== undefined;
  const existsInAssignmentManagement = hasEstablishedLoadingLocation && hasEstablishedUnloadingLocation;
  
  console.log(`   Assignment Loading Location ID: ${trip.loading_location_id}`);
  console.log(`   Assignment Unloading Location ID: ${trip.unloading_location_id}`);
  console.log(`   Has Established Loading Location: ${hasEstablishedLoadingLocation}`);
  console.log(`   Has Established Unloading Location: ${hasEstablishedUnloadingLocation}`);
  console.log(`   Exists in Assignment Management: ${existsInAssignmentManagement}`);
  
  // CORRECTED LOGIC: If assignment exists in Assignment Management, NEVER show Dynamic Route
  if (existsInAssignmentManagement) {
    console.log(`   ✅ CORRECTED RESULT: Normal route display (exists in Assignment Management)`);
    return false;
  }
  
  // Only show Dynamic Route for genuinely new assignments without established locations
  console.log(`   🔄 CORRECTED RESULT: Show Dynamic Route (genuinely new assignment)`);
  return true;
}

// Original logic for comparison
function testOriginalIsActiveDiscovery(trip) {
  console.log(`\n🔍 Testing ORIGINAL isActiveDiscovery logic for comparison:`);
  
  // Check if assignment is dynamic (auto-created)
  let assignmentNotes = {};
  try {
    assignmentNotes = trip.assignment_notes ? JSON.parse(trip.assignment_notes) : {};
  } catch (e) {
    console.log(`   ⚠️  Failed to parse assignment notes: ${trip.assignment_notes}`);
  }
  
  const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment' && 
                             assignmentNotes.auto_created === true;
  
  if (!isDynamicAssignment) {
    console.log(`   ✅ ORIGINAL RESULT: Normal route display (not dynamic assignment)`);
    return false;
  }
  
  // Check assignment age (recently created = < 30 minutes)
  const assignmentAge = trip.assignment_created_at ? 
    (Date.now() - new Date(trip.assignment_created_at).getTime()) / (1000 * 60) : null;
  const isRecentlyCreated = assignmentAge !== null && assignmentAge < 30;
  
  // Check if route discovery is complete
  const hasConfirmedLoadingLocation = trip.actual_loading_location_id && 
    ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status);
  const hasConfirmedUnloadingLocation = trip.actual_unloading_location_id && 
    ['unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status);
  
  const routeDiscoveryComplete = hasConfirmedLoadingLocation && hasConfirmedUnloadingLocation;
  
  // Original logic: Show dynamic route if recently created OR route discovery not complete
  const showDynamicRoute = isRecentlyCreated || !routeDiscoveryComplete;
  
  console.log(`   📊 ORIGINAL RESULT: ${showDynamicRoute ? '🔄 Dynamic Route' : 'Normal Route'}`);
  
  return showDynamicRoute;
}

async function testCorrectedLogic() {
  console.log('🔧 Testing Corrected Dynamic Route Logic');
  console.log('============================================================\n');
  
  try {
    // Test database connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log(`✅ Connected to database at ${testResult.rows[0].current_time}\n`);
    
    // Query trips with assignment data (matching the trips API query)
    console.log('📋 Querying trips with assignment data...');
    const tripsQuery = `
      SELECT 
        tl.id,
        tl.trip_number,
        tl.status,
        tl.actual_loading_location_id,
        tl.actual_unloading_location_id,
        tl.created_at as trip_created_at,
        a.id as assignment_id,
        a.loading_location_id,
        a.unloading_location_id,
        a.notes as assignment_notes,
        a.created_at as assignment_created_at,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        dt.truck_number,
        d.full_name as driver_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
      ORDER BY tl.created_at DESC
      LIMIT 10
    `;
    
    const result = await pool.query(tripsQuery);
    console.log(`✅ Found ${result.rows.length} recent trips\n`);
    
    if (result.rows.length === 0) {
      console.log('❌ No trips found in the last 7 days');
      return;
    }
    
    // Test each trip with both logics
    console.log('🔬 LOGIC COMPARISON ANALYSIS');
    console.log('============================================================');
    
    let fixedProblems = 0;
    let totalDynamicAssignments = 0;
    
    for (const trip of result.rows) {
      console.log(`\n📋 TRIP ${trip.id} - ${trip.truck_number} (${trip.driver_name})`);
      console.log(`   Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
      console.log(`   Status: ${trip.status}`);
      
      // Test original logic
      const originalResult = testOriginalIsActiveDiscovery(trip);
      
      // Test corrected logic
      const correctedResult = testCorrectedIsActiveDiscovery(trip);
      
      // Check if this is a dynamic assignment
      let assignmentNotes = {};
      try {
        assignmentNotes = trip.assignment_notes ? JSON.parse(trip.assignment_notes) : {};
      } catch (e) {}
      
      const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment' && 
                                 assignmentNotes.auto_created === true;
      
      if (isDynamicAssignment) {
        totalDynamicAssignments++;
        
        // Check if this assignment exists in Assignment Management
        const assignmentExistsQuery = `
          SELECT id, loading_location_id, unloading_location_id, status
          FROM assignments 
          WHERE id = $1 AND loading_location_id IS NOT NULL AND unloading_location_id IS NOT NULL
        `;
        const assignmentResult = await pool.query(assignmentExistsQuery, [trip.assignment_id]);
        const existsInAssignmentManagement = assignmentResult.rows.length > 0;
        
        console.log(`\n   🏢 Assignment Management Status: ${existsInAssignmentManagement ? 'EXISTS' : 'NOT FOUND'}`);
        console.log(`   📊 Original Logic: ${originalResult ? '🔄 Dynamic Route' : 'Normal Route'}`);
        console.log(`   🔧 Corrected Logic: ${correctedResult ? '🔄 Dynamic Route' : 'Normal Route'}`);
        
        // Identify if this is a fix
        if (originalResult && !correctedResult && existsInAssignmentManagement) {
          console.log(`   ✅ FIXED: Was showing Dynamic Route, now shows Normal Route (correct!)`);
          fixedProblems++;
        } else if (originalResult === correctedResult) {
          console.log(`   ✅ CONSISTENT: Both logics agree`);
        } else {
          console.log(`   ⚠️  DIFFERENT: Logic results differ`);
        }
      }
    }
    
    // Summary
    console.log('\n\n🎯 CORRECTED LOGIC TEST SUMMARY');
    console.log('============================================================');
    console.log(`📊 Total Dynamic Assignments Tested: ${totalDynamicAssignments}`);
    console.log(`🔧 Problems Fixed by Corrected Logic: ${fixedProblems}`);
    
    if (fixedProblems > 0) {
      console.log(`\n✅ SUCCESS: Corrected logic fixes ${fixedProblems} problematic case(s)!`);
      console.log('🎯 The new logic correctly identifies assignments that exist in Assignment Management');
      console.log('   and prevents them from showing Dynamic Route indicators.');
    } else {
      console.log(`\n✅ All assignments are already displaying correctly with current logic.`);
    }
    
    console.log('\n🔧 NEW LOGIC RULES:');
    console.log('1. ✅ If assignment is NOT dynamic (not auto-created) → Normal Route');
    console.log('2. ✅ If assignment exists in Assignment Management (has loading_location_id AND unloading_location_id) → Normal Route');
    console.log('3. 🔄 Only show Dynamic Route for genuinely new assignments without established locations');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the test
testCorrectedLogic().catch(console.error);
