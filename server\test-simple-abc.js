const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testSimpleABC() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Simple A→B→C Test\n');
    
    // Step 1: Find existing completed trip
    const completedTrip = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        a.truck_id, dt.truck_number,
        ll.name as loading_location_name, ul.name as unloading_location_name,
        a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status = 'trip_completed'
        AND tl.trip_completed_time > NOW() - INTERVAL '2 hours'
      ORDER BY tl.trip_completed_time DESC
      LIMIT 1
    `);
    
    if (completedTrip.rows.length === 0) {
      console.log('❌ No recent completed trips found');
      return;
    }
    
    const trip = completedTrip.rows[0];
    console.log(`📋 Found completed trip: ${trip.id}`);
    console.log(`  Truck: ${trip.truck_number}`);
    console.log(`  Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
    console.log(`  Status: ${trip.status}`);
    console.log(`  Completed: ${trip.trip_completed_time}`);
    
    // Step 2: Find a different loading location for extension
    const newLoadingLocation = await client.query(`
      SELECT id, name, type, location_code
      FROM locations 
      WHERE type = 'loading' 
        AND id != $1
      LIMIT 1
    `, [trip.loading_location_id]);
    
    if (newLoadingLocation.rows.length === 0) {
      console.log('❌ No alternative loading location found');
      return;
    }
    
    const locationC = newLoadingLocation.rows[0];
    console.log(`\n📍 Extension location: ${locationC.name} (${locationC.type})`);
    
    // Step 3: Check current trip count
    const beforeCount = await client.query(`
      SELECT COUNT(*) as total_trips
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
    `, [trip.truck_id]);
    
    console.log(`\n📊 Before scan: ${beforeCount.rows[0].total_trips} total trips`);
    
    // Step 4: Simulate the scanner call
    console.log(`\n🔍 Simulating truck scan at ${locationC.name}...`);
    
    // Make HTTP request to scanner endpoint
    const fetch = require('node-fetch');
    
    const scanData = {
      qrData: { type: 'truck', id: trip.truck_number },
      locationData: { type: 'location', id: locationC.location_code }
    };
    
    try {
      const response = await fetch('http://localhost:3001/api/scanner/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'test-client'
        },
        body: JSON.stringify(scanData)
      });
      
      const result = await response.json();
      
      console.log('📊 Scan Result:');
      console.log(`  Success: ${result.success}`);
      console.log(`  Message: ${result.message}`);
      console.log(`  Action: ${result.action || 'N/A'}`);
      console.log(`  Workflow Type: ${result.workflow_type || 'N/A'}`);
      
    } catch (error) {
      console.log('❌ Scanner request failed:', error.message);
      console.log('  This might be because the server is not running');
    }
    
    // Step 5: Check results
    const afterCount = await client.query(`
      SELECT COUNT(*) as total_trips
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
    `, [trip.truck_id]);
    
    console.log(`\n📊 After scan: ${afterCount.rows[0].total_trips} total trips`);
    
    // Check if new trip was created
    const newTrips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.workflow_type, tl.is_extended_trip, tl.baseline_trip_id,
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.created_at > NOW() - INTERVAL '5 minutes'
      ORDER BY tl.created_at DESC
    `, [trip.truck_id]);
    
    console.log(`\n📋 Recent trips for truck ${trip.truck_number}:`);
    newTrips.rows.forEach((t, index) => {
      const indicators = [];
      if (t.is_extended_trip) indicators.push('🔄 Extended');
      if (t.baseline_trip_id) indicators.push(`📎 Baseline: ${t.baseline_trip_id}`);
      
      console.log(`  ${index + 1}. Trip ${t.id}: ${t.status} | ${t.workflow_type} | ${t.loading_name} → ${t.unloading_name} | ${indicators.join(', ')}`);
    });
    
    // Verify expectations
    const expectedNewTrips = afterCount.rows[0].total_trips - beforeCount.rows[0].total_trips;
    console.log(`\n✅ Verification:`);
    console.log(`  New trips created: ${expectedNewTrips}`);
    console.log(`  Expected: 1 (for C→B extension)`);
    console.log(`  Success: ${expectedNewTrips === 1 ? '✅' : '❌'}`);
    
    // Check if original trip is preserved
    const originalTripCheck = await client.query(`
      SELECT 
        tl.id, tl.status,
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.id = $1
    `, [trip.id]);
    
    if (originalTripCheck.rows.length > 0) {
      const original = originalTripCheck.rows[0];
      console.log(`  Original trip preserved: ✅`);
      console.log(`  Original route: ${original.loading_name} → ${original.unloading_name}`);
      console.log(`  Route unchanged: ${original.loading_name === trip.loading_location_name && original.unloading_name === trip.unloading_location_name ? '✅' : '❌'}`);
    }
    
    console.log('\n🎉 Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testSimpleABC().catch(console.error);
