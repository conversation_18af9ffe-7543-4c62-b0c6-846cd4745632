#!/usr/bin/env node

/**
 * Create Test Extended Trip
 * 
 * This script creates a test extended trip to verify the multi-location
 * workflow display is working correctly.
 */

const { Pool } = require('pg');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
};

async function createTestExtendedTrip() {
  console.log('🧪 Creating Test Extended Trip...\n');

  const pool = new Pool(dbConfig);
  const client = await pool.connect();

  try {
    // 1. Get an existing completed trip to extend
    const baseTrip = await client.query(`
      SELECT tl.*, a.truck_id, a.driver_id, a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.status = 'trip_completed'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `);

    if (baseTrip.rows.length === 0) {
      console.log('❌ No completed trips found to extend');
      return;
    }

    const baseTripData = baseTrip.rows[0];
    console.log(`📋 Found base trip ${baseTripData.id} to extend`);

    // 2. Get a different loading location for the extension
    const newLocation = await client.query(`
      SELECT * FROM locations 
      WHERE type = 'loading' 
        AND id != $1 
      LIMIT 1
    `, [baseTripData.loading_location_id]);

    if (newLocation.rows.length === 0) {
      console.log('❌ No alternative loading location found');
      return;
    }

    const extensionLocation = newLocation.rows[0];
    console.log(`📍 Using ${extensionLocation.name} as extension location`);

    // 3. Create a new assignment for the extended trip (or use existing one)
    let assignment;
    try {
      const newAssignment = await client.query(`
        INSERT INTO assignments (
          truck_id, driver_id, loading_location_id, unloading_location_id,
          assigned_date, created_at, updated_at, notes
        )
        VALUES ($1, $2, $3, $4, CURRENT_DATE + INTERVAL '1 day', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $5)
        RETURNING *
      `, [
        baseTripData.truck_id,
        baseTripData.driver_id,
        extensionLocation.id,
        baseTripData.unloading_location_id,
        JSON.stringify({ creation_method: 'test_extended_trip', baseline_trip_id: baseTripData.id })
      ]);
      assignment = newAssignment.rows[0];
    } catch (error) {
      if (error.message.includes('duplicate key')) {
        // Use existing assignment
        const existingAssignment = await client.query(`
          SELECT * FROM assignments
          WHERE truck_id = $1 AND driver_id = $2
            AND loading_location_id = $3 AND unloading_location_id = $4
          LIMIT 1
        `, [
          baseTripData.truck_id,
          baseTripData.driver_id,
          extensionLocation.id,
          baseTripData.unloading_location_id
        ]);
        assignment = existingAssignment.rows[0];
        console.log(`📋 Using existing assignment ${assignment.id}`);
      } else {
        throw error;
      }
    }

    if (!assignment) {
      console.log('❌ Could not create or find assignment');
      return;
    }
    console.log(`✅ Using assignment ${assignment.id} for extended trip`);

    // 4. Get next trip number for this assignment
    const nextTripResult = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs
      WHERE assignment_id = $1
    `, [assignment.id]);

    const nextTripNumber = nextTripResult.rows[0].next_trip_number;
    console.log(`📊 Using trip number ${nextTripNumber} for extended trip`);

    // 5. Create the extended trip
    const extendedTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time,
        trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
        location_sequence,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING *
    `, [
      assignment.id,
      nextTripNumber, // trip_number
      'trip_completed',
      new Date(Date.now() - 3600000), // 1 hour ago
      new Date(Date.now() - 3300000), // 55 minutes ago
      new Date(Date.now() - 1800000), // 30 minutes ago
      new Date(Date.now() - 1500000), // 25 minutes ago
      new Date(Date.now() - 1200000), // 20 minutes ago
      extensionLocation.id,
      baseTripData.unloading_location_id,
      true, // is_extended_trip
      'extended', // workflow_type
      baseTripData.id, // baseline_trip_id
      1, // cycle_number
      JSON.stringify([
        {
          name: extensionLocation.name,
          type: 'loading',
          confirmed: true,
          location_id: extensionLocation.id
        },
        {
          name: 'Point B - Primary Dump Site', // Assuming this is the unloading location
          type: 'unloading',
          confirmed: true,
          location_id: baseTripData.unloading_location_id
        }
      ]),
      new Date(),
      new Date()
    ]);

    const newTrip = extendedTrip.rows[0];
    console.log(`✅ Created extended trip ${newTrip.id}`);

    // 6. Create a cycle trip as well
    const nextCycleTripNumber = nextTripNumber + 1;
    console.log(`📊 Using trip number ${nextCycleTripNumber} for cycle trip`);

    const cycleTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time,
        trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
        location_sequence,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING *
    `, [
      assignment.id,
      nextCycleTripNumber, // trip_number
      'trip_completed',
      new Date(Date.now() - 1000000), // 16 minutes ago
      new Date(Date.now() - 900000),  // 15 minutes ago
      new Date(Date.now() - 600000),  // 10 minutes ago
      new Date(Date.now() - 300000),  // 5 minutes ago
      new Date(Date.now() - 60000),   // 1 minute ago
      extensionLocation.id,
      baseTripData.unloading_location_id,
      true, // is_extended_trip
      'cycle', // workflow_type
      baseTripData.id, // baseline_trip_id
      2, // cycle_number
      JSON.stringify([
        {
          name: extensionLocation.name,
          type: 'loading',
          confirmed: true,
          location_id: extensionLocation.id
        },
        {
          name: 'Point B - Primary Dump Site',
          type: 'unloading',
          confirmed: true,
          location_id: baseTripData.unloading_location_id
        }
      ]),
      new Date(),
      new Date()
    ]);

    const cycleTrip2 = cycleTrip.rows[0];
    console.log(`✅ Created cycle trip ${cycleTrip2.id}`);

    console.log('\n📊 Test Data Summary:');
    console.log(`   Base trip: ${baseTripData.id} (standard)`);
    console.log(`   Extended trip: ${newTrip.id} (extended)`);
    console.log(`   Cycle trip: ${cycleTrip2.id} (cycle)`);
    console.log('\n✅ Test extended trips created successfully!');
    console.log('   You should now see multi-location workflows in the Trip Monitoring table.');

  } catch (error) {
    console.error('❌ Failed to create test extended trip:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  createTestExtendedTrip().catch(console.error);
}

module.exports = { createTestExtendedTrip };
