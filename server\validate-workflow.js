const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function validateWorkflow() {
  const client = await pool.connect();
  try {
    console.log('🧪 Multi-Location Workflow Validation Test\n');
    
    // 1. Check trip counting accuracy
    console.log('📊 Trip Counting Validation:');
    const tripCounts = await client.query(`
      SELECT 
        status,
        workflow_type,
        COUNT(*) as count,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as completed_count
      FROM trip_logs
      WHERE created_at > NOW() - INTERVAL '24 hours'
      GROUP BY status, workflow_type
      ORDER BY workflow_type, status
    `);
    
    tripCounts.rows.forEach(row => {
      console.log(`  ${row.workflow_type || 'standard'} - ${row.status}: ${row.count} trips`);
    });
    
    // 2. Check workflow relationships
    console.log('\n🔗 Workflow Relationships:');
    const workflows = await client.query(`
      SELECT 
        id,
        status,
        workflow_type,
        baseline_trip_id,
        cycle_number,
        is_extended_trip,
        location_sequence IS NOT NULL as has_sequence,
        notes::text LIKE '%extended_workflow%' as has_extended_notes,
        notes::text LIKE '%cycle_workflow%' as has_cycle_notes
      FROM trip_logs
      WHERE created_at > NOW() - INTERVAL '24 hours'
      ORDER BY created_at DESC
    `);
    
    workflows.rows.forEach(row => {
      const indicators = [];
      if (row.is_extended_trip) indicators.push('🔄 Extended');
      if (row.has_extended_notes) indicators.push('📝 Extended Notes');
      if (row.has_cycle_notes) indicators.push('📝 Cycle Notes');
      if (row.has_sequence) indicators.push('📍 Location Sequence');
      
      console.log(`  Trip ${row.id}: ${row.status} | ${row.workflow_type} | Cycle #${row.cycle_number} | ${indicators.join(', ')}`);
    });
    
    // 3. Validate status integrity
    console.log('\n✅ Status Integrity Check:');
    const statusCheck = await client.query(`
      SELECT DISTINCT status FROM trip_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
      ORDER BY status
    `);
    
    const validStatuses = ['assigned', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed', 'exception_pending', 'cancelled'];
    const foundStatuses = statusCheck.rows.map(r => r.status);
    const invalidStatuses = foundStatuses.filter(s => !validStatuses.includes(s));
    
    if (invalidStatuses.length === 0) {
      console.log('  ✅ All statuses are valid');
      foundStatuses.forEach(status => console.log(`    - ${status}`));
    } else {
      console.log('  ❌ Invalid statuses found:', invalidStatuses);
    }
    
    // 4. Check trip summary accuracy
    console.log('\n📈 Trip Summary Accuracy:');
    const summaryCheck = await client.query(`
      SELECT 
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as total_completed_trips,
        COUNT(CASE WHEN status = 'trip_completed' AND workflow_type = 'standard' THEN 1 END) as standard_completed,
        COUNT(CASE WHEN status = 'trip_completed' AND workflow_type = 'extended' THEN 1 END) as extended_completed,
        COUNT(CASE WHEN status = 'trip_completed' AND workflow_type = 'cycle' THEN 1 END) as cycle_completed
      FROM trip_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `);
    
    const summary = summaryCheck.rows[0];
    console.log(`  Total Completed Trips: ${summary.total_completed_trips}`);
    console.log(`  Standard Trips: ${summary.standard_completed}`);
    console.log(`  Extended Trips: ${summary.extended_completed}`);
    console.log(`  Cycle Trips: ${summary.cycle_completed}`);
    console.log(`  ✅ Each completed trip counts as +1 in summary`);
    
    console.log('\n🎉 Validation Complete!');
    
  } finally {
    client.release();
    await pool.end();
  }
}

validateWorkflow().catch(console.error);
