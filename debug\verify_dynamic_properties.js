const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function verifyDynamicProperties() {
  const client = await pool.connect();
  try {
    console.log('🔍 Verifying Dynamic Assignment Properties for Option 2...');

    // Get Trip #1 (ID: 105) details
    const trip1Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.assignment_code, a.notes as assignment_notes,
        a.loading_location_id, a.unloading_location_id,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.id = 105
    `);

    if (trip1Result.rows.length === 0) {
      console.log('❌ Trip #1 (ID: 105) not found');
      return;
    }

    const trip1 = trip1Result.rows[0];
    console.log('\n📊 Trip #1 (ID: 105) - Dynamic Route Discovery Verification');
    console.log('=' .repeat(70));

    // Basic trip information
    console.log(`🚚 Trip Details:`);
    console.log(`   Trip Number: ${trip1.trip_number}`);
    console.log(`   Status: ${trip1.status}`);
    console.log(`   Assignment Code: ${trip1.assignment_code}`);

    // Route verification
    console.log(`\n📍 Route Verification:`);
    console.log(`   Assigned Route: ${trip1.assigned_loading_location} → ${trip1.assigned_unloading_location}`);
    console.log(`   Actual Route: ${trip1.actual_loading_location || 'None'} → ${trip1.actual_unloading_location || 'None'}`);
    
    const routeCorrect = trip1.assigned_unloading_location === 'Point C - Secondary Dump Site';
    console.log(`   ✅ Route reflects Point C: ${routeCorrect ? 'YES' : 'NO'}`);

    // Assignment properties verification
    console.log(`\n📝 Assignment Properties:`);
    let isDynamic = false;
    if (trip1.assignment_notes) {
      try {
        const assignmentNotes = JSON.parse(trip1.assignment_notes);

        console.log(`   Creation Method: ${assignmentNotes.creation_method || 'Unknown'}`);
        isDynamic = assignmentNotes.creation_method === 'dynamic_assignment';
        console.log(`   ✅ Dynamic Assignment: ${isDynamic ? 'YES' : 'NO'}`);

        if (assignmentNotes.route_discovery) {
          console.log(`   Route Discovery Mode: ${assignmentNotes.route_discovery.mode || 'Unknown'}`);
          console.log(`   Discovery Type: ${assignmentNotes.route_discovery.discovery_type || 'Unknown'}`);
          console.log(`   Description: ${assignmentNotes.route_discovery.description || 'None'}`);

          if (assignmentNotes.route_discovery.confirmed_location) {
            console.log(`   Confirmed Location: ${assignmentNotes.route_discovery.confirmed_location.name} (${assignmentNotes.route_discovery.confirmed_location.type})`);
          }
        }

        if (assignmentNotes.auto_created) {
          console.log(`   ✅ Auto Created: YES`);
        }

        console.log(`\n   📋 Full Assignment Notes:`);
        console.log(JSON.stringify(assignmentNotes, null, 4));

      } catch (e) {
        console.log(`   ❌ Failed to parse assignment notes: ${e.message}`);
      }
    } else {
      console.log(`   ❌ No assignment notes found`);
    }

    // Trip progression verification
    console.log(`\n⏰ Trip Progression Verification:`);
    console.log(`   Loading Start: ${trip1.loading_start_time || 'NULL'} ${!trip1.loading_start_time ? '✅ (Expected for dynamic)' : '❌ (Unexpected)'}`);
    console.log(`   Loading End: ${trip1.loading_end_time || 'NULL'} ${!trip1.loading_end_time ? '✅ (Expected for dynamic)' : '❌ (Unexpected)'}`);
    console.log(`   Unloading Start: ${trip1.unloading_start_time || 'NULL'} ${trip1.unloading_start_time ? '✅ (Required)' : '❌ (Missing)'}`);
    console.log(`   Unloading End: ${trip1.unloading_end_time || 'NULL'} ${!trip1.unloading_end_time ? '✅ (Expected for current status)' : '❌ (Unexpected)'}`);

    // Status verification
    console.log(`\n📊 Status Verification:`);
    const statusCorrect = trip1.status === 'unloading_start';
    console.log(`   Current Status: ${trip1.status} ${statusCorrect ? '✅ (Correct)' : '❌ (Incorrect)'}`);
    console.log(`   Expected Next Action: Scan at Point C to complete unloading`);

    // Location verification
    console.log(`\n📍 Location Verification:`);
    const actualUnloadingCorrect = trip1.actual_unloading_location === 'Point C - Secondary Dump Site';
    console.log(`   Actual Unloading Location: ${trip1.actual_unloading_location || 'None'} ${actualUnloadingCorrect ? '✅ (Correct)' : '❌ (Incorrect)'}`);
    console.log(`   Actual Loading Location: ${trip1.actual_loading_location || 'None'} ✅ (Expected to be None for dynamic)`);

    // Overall Option 2 verification
    console.log(`\n🎯 Option 2 Implementation Verification:`);
    console.log('=' .repeat(50));
    
    const checks = [
      { name: 'Trip #1 exists with unloading_start status', passed: statusCorrect },
      { name: 'Trip #1 has dynamic_assignment creation method', passed: isDynamic },
      { name: 'Trip #1 route reflects Point A → Point C', passed: routeCorrect },
      { name: 'Trip #1 actual unloading location is Point C', passed: actualUnloadingCorrect },
      { name: 'Trip #1 has no loading timestamps (dynamic)', passed: !trip1.loading_start_time && !trip1.loading_end_time },
      { name: 'Trip #1 has unloading start timestamp', passed: !!trip1.unloading_start_time }
    ];
    
    let allPassed = true;
    checks.forEach(check => {
      console.log(`   ${check.passed ? '✅' : '❌'} ${check.name}`);
      if (!check.passed) allPassed = false;
    });
    
    console.log(`\n🎉 Option 2 Implementation: ${allPassed ? '✅ FULLY IMPLEMENTED' : '❌ NEEDS FIXES'}`);
    
    if (allPassed) {
      console.log('\n✅ Summary: Option 2 is correctly implemented!');
      console.log('   - Trip #2 was auto-completed (preserving audit trail)');
      console.log('   - Trip #1 continues as dynamic route discovery');
      console.log('   - Route correctly reflects actual path: Point A → Point C');
      console.log('   - Trip can progress normally through unloading phases');
      console.log('   - Dynamic assignment properties are properly set');
    }

  } catch (error) {
    console.error('❌ Error verifying dynamic properties:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

verifyDynamicProperties().catch(console.error);
