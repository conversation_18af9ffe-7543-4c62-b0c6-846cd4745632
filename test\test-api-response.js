#!/usr/bin/env node

/**
 * Test API Response
 * 
 * This script tests the trips API to see if it's returning
 * the multi-location workflow fields correctly.
 */

const http = require('http');

function testTripsAPI() {
  return new Promise((resolve, reject) => {
    console.log('🔍 Testing Trips API Response...\n');

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/trips?limit=2',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          console.log('📡 API Response Status:', res.statusCode);
          console.log('📊 Response Structure:');
          console.log('   - Success:', response.success);
          console.log('   - Data type:', typeof response.data);
          console.log('   - Data.data type:', typeof response.data?.data);
          console.log('   - Trips count:', response.data?.data?.length || 0);

          if (response.data?.data && Array.isArray(response.data.data)) {
            console.log('\n🔍 Checking Multi-Location Fields in Trips:');
            
            response.data.data.forEach((trip, index) => {
              console.log(`\n   Trip ${index + 1} (ID: ${trip.id}):`);
              console.log(`     - location_sequence: ${trip.location_sequence ? '✅ Present' : '❌ Missing'}`);
              console.log(`     - is_extended_trip: ${trip.is_extended_trip !== undefined ? '✅ Present' : '❌ Missing'}`);
              console.log(`     - workflow_type: ${trip.workflow_type ? '✅ Present' : '❌ Missing'}`);
              console.log(`     - baseline_trip_id: ${trip.baseline_trip_id !== undefined ? '✅ Present' : '❌ Missing'}`);
              console.log(`     - cycle_number: ${trip.cycle_number !== undefined ? '✅ Present' : '❌ Missing'}`);
              
              if (trip.location_sequence) {
                console.log(`     - location_sequence type: ${typeof trip.location_sequence}`);
                console.log(`     - location_sequence value:`, JSON.stringify(trip.location_sequence, null, 2));
              }
            });

            console.log('\n✅ API Test Complete');
            resolve(response);
          } else {
            console.log('❌ No trip data found in response');
            resolve(response);
          }
        } catch (error) {
          console.error('❌ Failed to parse API response:', error.message);
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Server not running on localhost:5000');
        console.log('   Please start the server with: cd server && npm run dev');
      } else {
        console.error('❌ API request failed:', error.message);
      }
      reject(error);
    });

    req.setTimeout(5000, () => {
      console.log('❌ API request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Test with authentication if needed
function testTripsAPIWithAuth() {
  return new Promise((resolve, reject) => {
    console.log('🔍 Testing Trips API with Authentication...\n');

    // First, try to get a token (simplified - in real app this would be login)
    const loginData = JSON.stringify({
      username: 'admin',
      password: 'admin123'
    });

    const loginOptions = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    };

    const loginReq = http.request(loginOptions, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const loginResponse = JSON.parse(data);
          
          if (loginResponse.success && loginResponse.data?.token) {
            console.log('✅ Authentication successful');
            
            // Now test trips API with token
            const tripsOptions = {
              hostname: 'localhost',
              port: 5000,
              path: '/api/trips?limit=2',
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${loginResponse.data.token}`
              }
            };

            const tripsReq = http.request(tripsOptions, (tripsRes) => {
              let tripsData = '';

              tripsRes.on('data', (chunk) => {
                tripsData += chunk;
              });

              tripsRes.on('end', () => {
                try {
                  const tripsResponse = JSON.parse(tripsData);
                  console.log('📡 Authenticated API Response Status:', tripsRes.statusCode);
                  
                  if (tripsResponse.data?.data && Array.isArray(tripsResponse.data.data)) {
                    console.log('✅ Got trips data with authentication');
                    resolve(tripsResponse);
                  } else {
                    console.log('❌ No trip data in authenticated response');
                    resolve(tripsResponse);
                  }
                } catch (error) {
                  console.error('❌ Failed to parse trips response:', error.message);
                  reject(error);
                }
              });
            });

            tripsReq.on('error', reject);
            tripsReq.end();
          } else {
            console.log('❌ Authentication failed');
            resolve(null);
          }
        } catch (error) {
          console.error('❌ Failed to parse login response:', error.message);
          reject(error);
        }
      });
    });

    loginReq.on('error', (error) => {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Server not running on localhost:5000');
      } else {
        console.error('❌ Login request failed:', error.message);
      }
      reject(error);
    });

    loginReq.write(loginData);
    loginReq.end();
  });
}

async function runAPITest() {
  try {
    // Try without auth first
    await testTripsAPI();
  } catch (error) {
    console.log('\n🔐 Trying with authentication...');
    try {
      await testTripsAPIWithAuth();
    } catch (authError) {
      console.log('❌ Both unauthenticated and authenticated requests failed');
    }
  }
}

// Run the test
if (require.main === module) {
  runAPITest().catch(console.error);
}

module.exports = { testTripsAPI, testTripsAPIWithAuth };
