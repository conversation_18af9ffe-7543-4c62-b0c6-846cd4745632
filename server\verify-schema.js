const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function verifySchema() {
  const client = await pool.connect();
  try {
    console.log('🔍 Phase 1: Database Schema Verification...\n');
    
    // Check columns
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND column_name IN ('location_sequence', 'is_extended_trip', 'workflow_type', 'baseline_trip_id', 'cycle_number')
      ORDER BY column_name
    `);
    
    console.log('✅ Multi-location workflow columns:');
    columns.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
    });
    
    // Check indexes
    const indexes = await client.query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'trip_logs' 
        AND (indexname LIKE '%workflow%' OR indexname LIKE '%location_sequence%' OR indexname LIKE '%baseline%')
    `);
    
    console.log('\n✅ Performance indexes:');
    indexes.rows.forEach(row => {
      console.log(`  ${row.indexname}`);
    });
    
    // Check constraints
    const constraints = await client.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'trip_logs' 
        AND (constraint_name LIKE '%workflow%' OR constraint_name LIKE '%cycle%' OR constraint_name LIKE '%baseline%')
    `);
    
    console.log('\n✅ Constraints:');
    constraints.rows.forEach(row => {
      console.log(`  ${row.constraint_name}: ${row.constraint_type}`);
    });
    
    // Check view
    const view = await client.query(`
      SELECT viewname FROM pg_views WHERE viewname = 'v_workflow_analytics'
    `);
    
    console.log(`\n✅ Analytics view: ${view.rows.length > 0 ? 'EXISTS' : 'MISSING'}`);
    
    // Check if migration was applied by looking for data
    const dataCheck = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN location_sequence IS NOT NULL THEN 1 END) as trips_with_sequence,
        COUNT(CASE WHEN is_extended_trip = true THEN 1 END) as extended_trips
      FROM trip_logs
    `);
    
    console.log('\n📊 Current data status:');
    const data = dataCheck.rows[0];
    console.log(`  Total trips: ${data.total_trips}`);
    console.log(`  Trips with location_sequence: ${data.trips_with_sequence}`);
    console.log(`  Extended trips: ${data.extended_trips}`);
    
    if (data.total_trips > 0 && data.trips_with_sequence === '0') {
      console.log('\n⚠️  Migration may need to be re-run to populate location_sequence data');
    } else {
      console.log('\n✅ Schema verification complete - all components present');
    }
    
  } finally {
    client.release();
    await pool.end();
  }
}

verifySchema().catch(console.error);
