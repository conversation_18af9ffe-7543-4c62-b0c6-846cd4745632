const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testAssignmentRetrieval() {
  const client = await pool.connect();
  try {
    console.log('🧪 Testing Assignment Retrieval...');

    // Test the exact getCurrentTripAndAssignment query for truck ID 1
    const truckId = 1;
    
    console.log(`\n1. Testing getCurrentTripAndAssignment query for truck ${truckId}:`);
    console.log('=' .repeat(60));

    const result = await client.query(`
      WITH current_trip AS (
        SELECT tl.*, tl.assignment_id as trip_assignment_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = $1
          AND tl.status NOT IN ('trip_completed', 'cancelled')
        ORDER BY tl.created_at DESC
        LIMIT 1
      ),
      trip_assignment AS (
        -- Get the assignment for the active trip (if any)
        SELECT
          a.id as assignment_id,
          a.truck_id,
          a.driver_id,
          a.status as assignment_status,
          a.priority,
          a.expected_loads_per_day,
          a.assigned_date,
          a.start_time,
          a.end_time,
          a.notes,
          a.created_at as assignment_created_at,
          ll.id as loading_location_id,
          ll.name as loading_location_name,
          ul.id as unloading_location_id,
          ul.name as unloading_location_name,
          ct.trip_assignment_id as active_assignment_id
        FROM current_trip ct
        JOIN assignments a ON ct.trip_assignment_id = a.id
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
      ),
      fallback_assignment AS (
        -- Fallback: get most recent assignment if no active trip
        SELECT
          a.id as assignment_id,
          a.truck_id,
          a.driver_id,
          a.status as assignment_status,
          a.priority,
          a.expected_loads_per_day,
          a.assigned_date,
          a.start_time,
          a.end_time,
          a.notes,
          a.created_at as assignment_created_at,
          ll.id as loading_location_id,
          ll.name as loading_location_name,
          ul.id as unloading_location_id,
          ul.name as unloading_location_name,
          a.id as active_assignment_id
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE a.truck_id = $1
          AND a.status IN ('assigned', 'in_progress')
          AND NOT EXISTS (SELECT 1 FROM current_trip)
        ORDER BY a.created_at DESC
        LIMIT 1
      ),
      combined_assignment AS (
        SELECT * FROM trip_assignment
        UNION ALL
        SELECT * FROM fallback_assignment
        LIMIT 1
      )
      SELECT
        ct.id as trip_id,
        ct.trip_assignment_id,
        ct.trip_number,
        ct.status as trip_status,
        ct.loading_start_time,
        ct.loading_end_time,
        ct.unloading_start_time,
        ct.unloading_end_time,
        ct.trip_completed_time,
        ct.actual_loading_location_id,
        ct.actual_unloading_location_id,
        ct.created_at as trip_created_at,
        ct.updated_at as trip_updated_at,
        ca.assignment_id,
        ca.truck_id,
        ca.driver_id,
        ca.assignment_status,
        ca.priority,
        ca.expected_loads_per_day,
        ca.assigned_date,
        ca.start_time,
        ca.end_time,
        ca.notes,
        ca.assignment_created_at,
        ca.loading_location_id,
        ca.loading_location_name,
        ca.unloading_location_id,
        ca.unloading_location_name,
        ca.active_assignment_id
      FROM current_trip ct
      LEFT JOIN combined_assignment ca ON ct.trip_assignment_id = ca.assignment_id
      
      UNION ALL
      
      SELECT
        NULL as trip_id,
        NULL as trip_assignment_id,
        NULL as trip_number,
        NULL as trip_status,
        NULL as loading_start_time,
        NULL as loading_end_time,
        NULL as unloading_start_time,
        NULL as unloading_end_time,
        NULL as trip_completed_time,
        NULL as actual_loading_location_id,
        NULL as actual_unloading_location_id,
        NULL as trip_created_at,
        NULL as trip_updated_at,
        ca.assignment_id,
        ca.truck_id,
        ca.driver_id,
        ca.assignment_status,
        ca.priority,
        ca.expected_loads_per_day,
        ca.assigned_date,
        ca.start_time,
        ca.end_time,
        ca.notes,
        ca.assignment_created_at,
        ca.loading_location_id,
        ca.loading_location_name,
        ca.unloading_location_id,
        ca.unloading_location_name,
        ca.active_assignment_id
      FROM combined_assignment ca
      WHERE NOT EXISTS (SELECT 1 FROM current_trip)
      
      ORDER BY trip_id DESC NULLS LAST
      LIMIT 1
    `, [truckId]);

    if (result.rows.length === 0) {
      console.log('❌ No results returned from getCurrentTripAndAssignment query');
      return;
    }

    const data = result.rows[0];
    console.log('\n📊 Query Results:');
    console.log(`Trip ID: ${data.trip_id}`);
    console.log(`Trip Status: ${data.trip_status}`);
    console.log(`Assignment ID: ${data.assignment_id}`);
    console.log(`Assignment Notes Type: ${typeof data.notes}`);
    console.log(`Assignment Notes Length: ${data.notes ? data.notes.length : 0}`);
    
    if (data.notes) {
      console.log('\n📝 Assignment Notes Content:');
      console.log(data.notes);
      
      console.log('\n🔍 Testing Dynamic Assignment Detection:');
      try {
        const assignmentNotes = JSON.parse(data.notes || '{}');
        console.log('   Parsed successfully ✅');
        console.log(`   creation_method: "${assignmentNotes.creation_method}"`);
        const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
        console.log(`   isDynamicAssignment: ${isDynamicAssignment}`);
        
        if (isDynamicAssignment) {
          console.log('   ✅ Dynamic assignment detected correctly!');
        } else {
          console.log('   ❌ Dynamic assignment NOT detected');
        }
      } catch (error) {
        console.log(`   ❌ Parse error: ${error.message}`);
      }
    } else {
      console.log('\n❌ Assignment notes are NULL or empty!');
      console.log('   This is the problem - notes field is not being retrieved');
    }

    // Test the trip validation logic
    console.log('\n🔍 Testing Trip Validation Logic:');
    const hasLoadingSteps = !!(data.loading_start_time && data.loading_end_time);
    const hasUnloadingSteps = !!(data.unloading_start_time && data.unloading_end_time);
    
    console.log(`   Has Loading Steps: ${hasLoadingSteps}`);
    console.log(`   Has Unloading Steps: ${hasUnloadingSteps}`);
    
    let isDynamicAssignment = false;
    if (data.notes) {
      try {
        const assignmentNotes = JSON.parse(data.notes || '{}');
        isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
      } catch (e) {
        // Ignore
      }
    }
    
    console.log(`   Is Dynamic Assignment: ${isDynamicAssignment}`);
    
    if (isDynamicAssignment) {
      if (hasUnloadingSteps) {
        console.log('   ✅ Dynamic validation would PASS');
      } else {
        console.log('   ❌ Dynamic validation would FAIL (missing unloading steps)');
      }
    } else {
      if (hasLoadingSteps && hasUnloadingSteps) {
        console.log('   ✅ Traditional validation would PASS');
      } else {
        console.log('   ❌ Traditional validation would FAIL (missing loading/unloading steps)');
        const missingSteps = [];
        if (!data.loading_start_time) missingSteps.push("loading start");
        if (!data.loading_end_time) missingSteps.push("loading end");
        if (!data.unloading_start_time) missingSteps.push("unloading start");
        if (!data.unloading_end_time) missingSteps.push("unloading end");
        console.log(`   Missing steps: ${missingSteps.join(", ")}`);
      }
    }

  } catch (error) {
    console.error('❌ Error testing assignment retrieval:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testAssignmentRetrieval().catch(console.error);
