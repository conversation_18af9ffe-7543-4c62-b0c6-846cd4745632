#!/usr/bin/env node

/**
 * JSON Parsing Fix Test
 * 
 * This test verifies that the JSON parsing error in TripsTable is fixed
 * by testing various edge cases that could cause the error.
 */

// Simulate the parsing logic from TripsTable.js
function safeParseLocationSequence(locationSequence) {
  if (!locationSequence) {
    return null;
  }

  try {
    // Handle both string and object cases
    if (typeof locationSequence === 'string') {
      const trimmed = locationSequence.trim();
      
      // Check for common problematic values
      if (trimmed === '[object Object]' || trimmed === '[object Array]' || trimmed === 'undefined' || trimmed === 'null') {
        console.warn('Detected problematic location_sequence value:', trimmed);
        return null;
      } else if (trimmed && (trimmed.startsWith('[') || trimmed.startsWith('{'))) {
        // Only parse if it's a non-empty string that looks like valid JSON
        return JSON.parse(trimmed);
      }
    } else if (typeof locationSequence === 'object') {
      if (Array.isArray(locationSequence)) {
        return locationSequence;
      } else if (locationSequence !== null) {
        // Handle case where it's an object but not an array
        console.warn('location_sequence is an object but not an array:', locationSequence);
        return null;
      }
    }
    return null;
  } catch (error) {
    console.warn('Failed to parse location_sequence:', error);
    return null;
  }
}

function validateLocationSequence(locationSequence) {
  if (!locationSequence || !Array.isArray(locationSequence) || locationSequence.length === 0) {
    return false;
  }

  // Additional validation: ensure all items in the array are valid location objects
  return locationSequence.every(loc => 
    loc && typeof loc === 'object' && loc.name && loc.type
  );
}

function testJSONParsingFix() {
  console.log('🧪 Testing JSON Parsing Fix for TripsTable...\n');

  const testCases = [
    // Cases that previously caused errors
    {
      name: 'Object toString (main culprit)',
      value: '[object Object]',
      shouldParse: false,
      description: 'This is what caused the original error'
    },
    {
      name: 'Object toString Array',
      value: '[object Array]',
      shouldParse: false,
      description: 'Another variant of object toString'
    },
    
    // Valid cases
    {
      name: 'Valid JSON array string',
      value: '[{"name":"Point A","type":"loading","confirmed":true}]',
      shouldParse: true,
      description: 'Properly formatted JSON string'
    },
    {
      name: 'Valid array object',
      value: [{"name":"Point A","type":"loading","confirmed":true}],
      shouldParse: true,
      description: 'Already parsed array'
    },
    
    // Edge cases
    {
      name: 'Empty string',
      value: '',
      shouldParse: false,
      description: 'Empty string should not parse'
    },
    {
      name: 'Null value',
      value: null,
      shouldParse: false,
      description: 'Null should not parse'
    },
    {
      name: 'Undefined value',
      value: undefined,
      shouldParse: false,
      description: 'Undefined should not parse'
    },
    {
      name: 'Invalid JSON string',
      value: '{invalid: json}',
      shouldParse: false,
      description: 'Malformed JSON should not parse'
    },
    {
      name: 'Plain object (not array)',
      value: {"name":"Point A","type":"loading"},
      shouldParse: false,
      description: 'Object should not be treated as location sequence'
    },
    {
      name: 'Array with invalid location objects',
      value: [{"invalid": "object"}, {"name": "Point A"}],
      shouldParse: false,
      description: 'Array with invalid location structure'
    }
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log(`  Description: ${testCase.description}`);
    
    try {
      const parsed = safeParseLocationSequence(testCase.value);
      const isValid = validateLocationSequence(parsed);
      const actualResult = parsed !== null && isValid;
      
      if (actualResult === testCase.shouldParse) {
        console.log(`  ✅ PASS - Expected: ${testCase.shouldParse}, Got: ${actualResult}`);
        passed++;
      } else {
        console.log(`  ❌ FAIL - Expected: ${testCase.shouldParse}, Got: ${actualResult}`);
        console.log(`  Parsed value:`, parsed);
        failed++;
      }
    } catch (error) {
      console.log(`  ❌ ERROR - ${error.message}`);
      failed++;
    }
    
    console.log('');
  });

  console.log('📊 Test Results:');
  console.log(`  ✅ Passed: ${passed}`);
  console.log(`  ❌ Failed: ${failed}`);
  console.log(`  📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! The JSON parsing fix should resolve the error.');
  } else {
    console.log('\n⚠️  Some tests failed. The fix may need additional work.');
  }

  return { passed, failed };
}

// Run the test
if (require.main === module) {
  testJSONParsingFix();
}

module.exports = { testJSONParsingFix, safeParseLocationSequence, validateLocationSequence };
