const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeCompletionError() {
  const client = await pool.connect();
  try {
    console.log('🔍 Analyzing Trip Completion Error...');

    // Get current Trip #1 state
    const trip1Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.assignment_code, a.notes as assignment_notes,
        a.loading_location_id, a.unloading_location_id,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.id = 105
    `);

    if (trip1Result.rows.length === 0) {
      console.log('❌ Trip #1 (ID: 105) not found');
      return;
    }

    const trip1 = trip1Result.rows[0];
    console.log('\n📊 Current Trip #1 State:');
    console.log('=' .repeat(50));
    console.log(`Trip ID: ${trip1.id}`);
    console.log(`Trip Number: ${trip1.trip_number}`);
    console.log(`Status: ${trip1.status}`);
    console.log(`Assignment: ${trip1.assignment_code}`);
    console.log('');

    console.log('📍 Route Information:');
    console.log(`Assigned Route: ${trip1.assigned_loading_location} → ${trip1.assigned_unloading_location}`);
    console.log(`Loading Location ID: ${trip1.loading_location_id}`);
    console.log(`Unloading Location ID: ${trip1.unloading_location_id}`);
    console.log('');

    console.log('⏰ Timestamp Analysis:');
    console.log(`Loading Start: ${trip1.loading_start_time || 'NULL'} ${trip1.loading_start_time ? '✅' : '❌'}`);
    console.log(`Loading End: ${trip1.loading_end_time || 'NULL'} ${trip1.loading_end_time ? '✅' : '❌'}`);
    console.log(`Unloading Start: ${trip1.unloading_start_time || 'NULL'} ${trip1.unloading_start_time ? '✅' : '❌'}`);
    console.log(`Unloading End: ${trip1.unloading_end_time || 'NULL'} ${trip1.unloading_end_time ? '✅' : '❌'}`);
    console.log(`Trip Completed: ${trip1.trip_completed_time || 'NULL'} ${trip1.trip_completed_time ? '✅' : '❌'}`);
    console.log('');

    // Check if this is a dynamic assignment
    let isDynamicAssignment = false;
    if (trip1.assignment_notes) {
      try {
        const assignmentNotes = JSON.parse(trip1.assignment_notes);
        isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
        console.log('📝 Assignment Analysis:');
        console.log(`Creation Method: ${assignmentNotes.creation_method}`);
        console.log(`Is Dynamic Assignment: ${isDynamicAssignment ? 'Yes' : 'No'}`);
      } catch (e) {
        console.log('❌ Failed to parse assignment notes');
      }
    }
    console.log('');

    // Simulate the validation logic that's causing the error
    console.log('🔍 Simulating Validation Logic:');
    console.log('=' .repeat(40));

    console.log('\n1. Current handleUnloadingEnd validation logic:');
    console.log('   Checking for ALL required steps...');
    
    const missingSteps = [];
    if (!trip1.loading_start_time) missingSteps.push("loading start");
    if (!trip1.loading_end_time) missingSteps.push("loading end");
    if (!trip1.unloading_start_time) missingSteps.push("unloading start");
    if (!trip1.unloading_end_time) missingSteps.push("unloading end");
    
    console.log(`   Missing steps: ${missingSteps.join(", ")}`);
    
    if (missingSteps.length > 0) {
      console.log(`   ❌ VALIDATION FAILS: "Cannot complete trip: Missing required steps: ${missingSteps.join(", ")}"`);
      console.log('   This is the error you\'re seeing!');
    } else {
      console.log('   ✅ Validation would pass');
    }

    console.log('\n2. The Problem:');
    console.log('   ❌ Current validation checks for ALL steps (loading + unloading)');
    console.log('   ❌ But Trip #1 is dynamic route discovery (unloading-only)');
    console.log('   ❌ It legitimately doesn\'t have loading steps');
    console.log('   ❌ The validation logic doesn\'t account for this scenario');

    console.log('\n3. What Should Happen:');
    console.log('   ✅ For dynamic assignments, only check unloading steps');
    console.log('   ✅ Trip #1 has unloading_start_time and unloading_end_time');
    console.log('   ✅ It should be allowed to complete at Point A (loading location)');

    console.log('\n4. Current Scenario Analysis:');
    console.log(`   User Action: Scanning at Point A (${trip1.assigned_loading_location})`);
    console.log(`   Trip Status: ${trip1.status} (unloading complete)`);
    console.log(`   Expected Behavior: Complete the trip successfully`);
    console.log(`   Actual Behavior: Validation error due to missing loading steps`);

    // Check Point A location details
    const pointAResult = await client.query(`
      SELECT id, name, type FROM locations WHERE id = $1
    `, [trip1.loading_location_id]);

    if (pointAResult.rows.length > 0) {
      const pointA = pointAResult.rows[0];
      console.log('\n📍 Point A Location Details:');
      console.log(`   Location: ${pointA.name}`);
      console.log(`   Type: ${pointA.type}`);
      console.log(`   ID: ${pointA.id}`);
      console.log(`   ✅ This is a valid loading location for trip completion`);
    }

    console.log('\n🔧 Required Fix:');
    console.log('=' .repeat(30));
    console.log('1. Update handleUnloadingEnd validation logic');
    console.log('2. Add dynamic assignment detection');
    console.log('3. For dynamic assignments, only validate unloading steps');
    console.log('4. Allow completion at loading locations for dynamic trips');
    console.log('5. Preserve the existing logic for traditional trips');

    console.log('\n📋 Fix Implementation Plan:');
    console.log('1. Check if assignment is dynamic (creation_method = "dynamic_assignment")');
    console.log('2. If dynamic: only require unloading_start_time and unloading_end_time');
    console.log('3. If traditional: require all steps (current logic)');
    console.log('4. Allow trip completion at loading locations for both scenarios');

  } catch (error) {
    console.error('❌ Error analyzing completion error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeCompletionError().catch(console.error);
