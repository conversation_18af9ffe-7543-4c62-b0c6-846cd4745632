const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function checkWorkflow() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking Multi-Location Workflow Status...\n');
    
    // Check schema
    const schema = await client.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND column_name IN ('location_sequence', 'is_extended_trip', 'workflow_type', 'baseline_trip_id', 'cycle_number')
      ORDER BY column_name
    `);
    console.log('✅ Schema columns found:', schema.rows.map(r => r.column_name));
    
    // Check data
    const data = await client.query(`
      SELECT id, status, is_extended_trip, workflow_type, 
             location_sequence IS NOT NULL as has_sequence,
             CASE 
               WHEN location_sequence IS NOT NULL THEN jsonb_array_length(location_sequence)
               ELSE 0 
             END as sequence_length
      FROM trip_logs 
      ORDER BY created_at DESC LIMIT 5
    `);
    
    console.log('\n📊 Recent trips:');
    data.rows.forEach(row => {
      console.log(`  Trip ${row.id}: ${row.status} | Extended: ${row.is_extended_trip} | Workflow: ${row.workflow_type} | Sequence: ${row.has_sequence} (${row.sequence_length} locations)`);
    });

    // Check for extended trips specifically
    const extendedTrips = await client.query(`
      SELECT id, workflow_type, cycle_number, baseline_trip_id
      FROM trip_logs 
      WHERE is_extended_trip = true
      ORDER BY created_at DESC
    `);
    
    console.log(`\n🔄 Extended trips found: ${extendedTrips.rows.length}`);
    extendedTrips.rows.forEach(row => {
      console.log(`  Trip ${row.id}: ${row.workflow_type} | Cycle: ${row.cycle_number} | Baseline: ${row.baseline_trip_id}`);
    });

  } finally {
    client.release();
    await pool.end();
  }
}

checkWorkflow().catch(console.error);
