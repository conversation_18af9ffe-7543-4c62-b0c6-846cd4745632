const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testDirectABC() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Direct A→B→C Test\n');
    
    // Step 1: Find existing completed trip
    const completedTrip = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        a.truck_id, dt.truck_number,
        ll.name as loading_location_name, ul.name as unloading_location_name,
        a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status = 'trip_completed'
        AND tl.trip_completed_time > NOW() - INTERVAL '2 hours'
      ORDER BY tl.trip_completed_time DESC
      LIMIT 1
    `);
    
    if (completedTrip.rows.length === 0) {
      console.log('❌ No recent completed trips found');
      return;
    }
    
    const trip = completedTrip.rows[0];
    console.log(`📋 Found completed trip: ${trip.id}`);
    console.log(`  Truck: ${trip.truck_number}`);
    console.log(`  Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
    console.log(`  Status: ${trip.status}`);
    
    // Step 2: Find a different loading location for extension
    const newLoadingLocation = await client.query(`
      SELECT id, name, type, location_code
      FROM locations 
      WHERE type = 'loading' 
        AND id != $1
      LIMIT 1
    `, [trip.loading_location_id]);
    
    if (newLoadingLocation.rows.length === 0) {
      console.log('❌ No alternative loading location found');
      return;
    }
    
    const locationC = newLoadingLocation.rows[0];
    console.log(`\n📍 Extension location: ${locationC.name} (${locationC.type})`);
    
    // Step 3: Get truck and location objects
    const truckData = await client.query(`
      SELECT id, truck_number, license_plate, status
      FROM dump_trucks 
      WHERE truck_number = $1
    `, [trip.truck_number]);
    
    const truck = truckData.rows[0];
    
    // Step 4: Test the checkRecentCompletedTrip logic manually
    console.log(`\n🔍 Testing post-completion detection...`);
    
    // Check for recent completed trips (simulate checkRecentCompletedTrip)
    const recentCompletedTrips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        ll.name as loading_location_name, ul.name as unloading_location_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON tl.actual_loading_location_id = ll.id
      LEFT JOIN locations ul ON tl.actual_unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.status = 'trip_completed'
        AND tl.trip_completed_time > NOW() - INTERVAL '30 minutes'
      ORDER BY tl.trip_completed_time DESC
      LIMIT 1
    `, [truck.id]);
    
    if (recentCompletedTrips.rows.length > 0) {
      const recentTrip = recentCompletedTrips.rows[0];
      console.log(`  ✅ Found recent completed trip: ${recentTrip.id}`);
      console.log(`    Route: ${recentTrip.loading_location_name} → ${recentTrip.unloading_location_name}`);
      console.log(`    Completed: ${recentTrip.trip_completed_time}`);
      
      // Test workflow type determination
      let workflowType = 'none';
      if (locationC.id !== recentTrip.actual_loading_location_id &&
          locationC.id !== recentTrip.actual_unloading_location_id) {
        workflowType = 'extended'; // A→B→C extension
      } else if (locationC.id === recentTrip.actual_unloading_location_id) {
        workflowType = 'cycle'; // C→B→C cycle
      }
      
      console.log(`    Workflow type: ${workflowType}`);
      
      if (workflowType === 'extended') {
        console.log(`\n🔄 Should create extended workflow: ${recentTrip.loading_location_name}→${recentTrip.unloading_location_name}→${locationC.name}`);
        
        // Test if we can create the extended trip manually
        console.log(`\n📋 Testing extended trip creation...`);
        
        // Create new assignment for C→B
        const newAssignment = await client.query(`
          INSERT INTO assignments (
            assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
            assigned_date, status, priority, expected_loads_per_day, notes
          )
          SELECT 
            'EXT-' || EXTRACT(EPOCH FROM NOW())::text,
            $1, 
            (SELECT driver_id FROM assignments WHERE id = (
              SELECT assignment_id FROM trip_logs WHERE id = $2
            )),
            $3, $4,
            CURRENT_DATE, 'assigned', 'normal', 1,
            $5
          RETURNING *
        `, [
          truck.id, 
          recentTrip.id,
          locationC.id, 
          recentTrip.actual_unloading_location_id,
          JSON.stringify({
            creation_method: 'extended_workflow',
            baseline_trip_id: recentTrip.id,
            workflow_type: 'extended'
          })
        ]);
        
        const extendedAssignment = newAssignment.rows[0];
        console.log(`    ✅ Created extended assignment: ${extendedAssignment.assignment_code}`);
        
        // Create new trip for the extended workflow
        const newTrip = await client.query(`
          INSERT INTO trip_logs (
            assignment_id, trip_number, status, 
            is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
            location_sequence
          )
          VALUES ($1, 1, 'loading_start', true, 'extended', $2, 1, $3)
          RETURNING *
        `, [
          extendedAssignment.id,
          recentTrip.id,
          JSON.stringify([
            { name: locationC.name, type: 'loading', confirmed: true, location_id: locationC.id },
            { name: recentTrip.unloading_location_name, type: 'unloading', confirmed: false, location_id: recentTrip.actual_unloading_location_id }
          ])
        ]);
        
        const extendedTrip = newTrip.rows[0];
        console.log(`    ✅ Created extended trip: ${extendedTrip.id}`);
        
        // Mark baseline trip as extended
        await client.query(`
          UPDATE trip_logs
          SET notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb
          WHERE id = $2
        `, [
          JSON.stringify({
            extended_workflow: true,
            extended_at: new Date().toISOString(),
            extended_trip_id: extendedTrip.id,
            workflow_continuation: 'extended_to_next_location'
          }),
          recentTrip.id
        ]);
        
        console.log(`    ✅ Marked baseline trip as extended`);
        
        // Verify the results
        const verification = await client.query(`
          SELECT 
            tl.id, tl.status, tl.workflow_type, tl.is_extended_trip, tl.baseline_trip_id,
            ll.name as loading_name, ul.name as unloading_name,
            a.assignment_code
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          WHERE a.truck_id = $1
          ORDER BY tl.created_at DESC
          LIMIT 3
        `, [truck.id]);
        
        console.log(`\n📊 Verification Results:`);
        verification.rows.forEach((t, index) => {
          const indicators = [];
          if (t.is_extended_trip) indicators.push('🔄 Extended');
          if (t.baseline_trip_id) indicators.push(`📎 Baseline: ${t.baseline_trip_id}`);
          
          console.log(`  ${index + 1}. Trip ${t.id}: ${t.status} | ${t.workflow_type} | ${t.loading_name} → ${t.unloading_name} | ${indicators.join(', ')}`);
        });
        
        console.log(`\n✅ Manual A→B→C workflow creation successful!`);
        console.log(`   This proves the workflow logic works when implemented correctly.`);
        console.log(`   The issue is in the scanner.js processTruckScan function.`);
        
      }
    } else {
      console.log(`  ❌ No recent completed trips found`);
    }
    
    console.log('\n🎉 Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testDirectABC().catch(console.error);
