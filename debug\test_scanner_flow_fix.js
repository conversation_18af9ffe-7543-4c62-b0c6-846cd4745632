const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testScannerFlowFix() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 TESTING COMPLETE SCANNER FLOW WITH AUTO-ASSIGNMENT');
    console.log('=' .repeat(70));
    
    // Step 1: Clear any existing assignments for DT-100 to force auto-creation
    console.log('\n🧹 Clearing existing assignments for DT-100...');
    await client.query(`
      DELETE FROM assignments 
      WHERE truck_id = (SELECT id FROM dump_trucks WHERE truck_number = 'DT-100')
        AND status = 'assigned'
        AND (loading_location_id = 3 OR unloading_location_id = 3)
    `);
    console.log('✅ Cleared existing assignments');
    
    // Step 2: Ensure DT-100 has at least one historical assignment for pattern learning
    console.log('\n📋 Checking historical assignments for DT-100...');
    const historicalCheck = await client.query(`
      SELECT COUNT(*) as count
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
        AND a.status IN ('assigned', 'in_progress', 'completed')
    `);
    
    const historicalCount = parseInt(historicalCheck.rows[0].count);
    console.log(`📊 Historical assignments found: ${historicalCount}`);
    
    if (historicalCount === 0) {
      console.log('⚠️  No historical assignments found. Creating a base assignment...');
      
      // Create a base assignment for pattern learning
      await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
          assigned_date, status, priority, expected_loads_per_day, notes, created_at, updated_at
        ) VALUES (
          'BASE-TEST-' || EXTRACT(EPOCH FROM NOW()), 
          (SELECT id FROM dump_trucks WHERE truck_number = 'DT-100'),
          (SELECT id FROM drivers LIMIT 1),
          1, 2, -- Point A to Point B
          CURRENT_DATE, 'completed', 'medium', 1,
          '{"creation_method": "test_base"}',
          NOW(), NOW()
        )
      `);
      console.log('✅ Base assignment created');
    }
    
    // Step 3: Test the specific scenario that was failing
    console.log('\n🎯 Testing DT-100 scanning Point C - Secondary Dump Site...');
    
    // Simulate the scanner.js processTruckScan logic
    const truck = await client.query(`
      SELECT id, truck_number, status FROM dump_trucks WHERE truck_number = 'DT-100'
    `);
    
    const location = await client.query(`
      SELECT id, name, type, location_code FROM locations WHERE location_code = 'LOC-003'
    `);
    
    if (truck.rows.length === 0 || location.rows.length === 0) {
      console.log('❌ Required data not found');
      return;
    }
    
    const truckData = truck.rows[0];
    const locationData = location.rows[0];
    
    console.log(`📍 Truck: ${truckData.truck_number} (ID: ${truckData.id})`);
    console.log(`📍 Location: ${locationData.name} (ID: ${locationData.id}, Type: ${locationData.type})`);
    
    // Step 4: Check for existing assignments (this should find none after cleanup)
    const existingAssignments = await client.query(`
      SELECT COUNT(*) as count
      FROM assignments a
      WHERE a.truck_id = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
    `, [truckData.id, locationData.id]);
    
    const existingCount = parseInt(existingAssignments.rows[0].count);
    console.log(`📊 Existing assignments for this truck+location: ${existingCount}`);
    
    if (existingCount === 0) {
      console.log('🚀 No existing assignment found. Testing auto-assignment creation...');
      
      // Import and test AutoAssignmentCreator
      const { AutoAssignmentCreator } = require('../server/utils/AutoAssignmentCreator');
      const autoAssignmentCreator = new AutoAssignmentCreator();
      
      try {
        // Test shouldCreateAutoAssignment first
        const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
          truck: truckData,
          location: locationData,
          client
        });
        
        console.log('📋 shouldCreateAutoAssignment result:');
        console.log(`   Should Create: ${shouldCreateCheck.shouldCreate}`);
        console.log(`   Reason: ${shouldCreateCheck.reason}`);
        
        if (shouldCreateCheck.shouldCreate) {
          console.log('✅ Proceeding with auto-assignment creation...');
          
          const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
            truck: truckData,
            location: locationData,
            client,
            userId: 1,
            enableDynamicRouting: true
          });
          
          console.log('🎉 AUTO-ASSIGNMENT CREATED SUCCESSFULLY!');
          console.log(`   Assignment Code: ${autoAssignment.assignment_code}`);
          console.log(`   Assignment ID: ${autoAssignment.id}`);
          console.log(`   Status: ${autoAssignment.status}`);
          console.log(`   Loading Location: ${autoAssignment.loading_location_name || 'TBD'}`);
          console.log(`   Unloading Location: ${autoAssignment.unloading_location_name || 'TBD'}`);
          
          // Verify the assignment was created correctly
          const verifyAssignment = await client.query(`
            SELECT 
              a.*, 
              ll.name as loading_location_name,
              ul.name as unloading_location_name
            FROM assignments a
            LEFT JOIN locations ll ON a.loading_location_id = ll.id
            LEFT JOIN locations ul ON a.unloading_location_id = ul.id
            WHERE a.id = $1
          `, [autoAssignment.id]);
          
          if (verifyAssignment.rows.length > 0) {
            const assignment = verifyAssignment.rows[0];
            console.log('✅ Assignment verification successful:');
            console.log(`   Route: ${assignment.loading_location_name} → ${assignment.unloading_location_name}`);
            console.log(`   Notes: ${assignment.notes}`);
          }
          
          // Clean up test assignment
          await client.query('DELETE FROM assignments WHERE id = $1', [autoAssignment.id]);
          console.log('🧹 Test assignment cleaned up');
          
        } else {
          console.log('ℹ️  Auto-assignment creation not recommended');
        }
        
      } catch (autoAssignmentError) {
        console.log('❌ AUTO-ASSIGNMENT CREATION FAILED:');
        console.log(`   Error: ${autoAssignmentError.message}`);
        
        // Check for the specific parameter binding error
        if (autoAssignmentError.message.includes('bind message supplies') && 
            autoAssignmentError.message.includes('parameters')) {
          console.log('🚨 PARAMETER BINDING ERROR DETECTED!');
          console.log('   This indicates the fix was not successful.');
        } else {
          console.log('ℹ️  This is a different error, not the parameter binding issue.');
        }
        
        throw autoAssignmentError;
      }
      
    } else {
      console.log('ℹ️  Existing assignment found, auto-creation not needed');
    }
    
    console.log('\n✅ SCANNER FLOW TEST COMPLETED SUCCESSFULLY');
    console.log('🎯 The parameter binding error has been fixed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testScannerFlowFix().catch(console.error);
