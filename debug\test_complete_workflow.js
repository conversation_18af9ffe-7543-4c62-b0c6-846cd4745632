const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testCompleteWorkflow() {
  const client = await pool.connect();
  try {
    console.log('🔄 Testing Complete Dynamic Route Discovery Workflow...');

    // Step 1: Check current trip state
    console.log('\n📊 Step 1: Current Trip State');
    const tripResult = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.notes as assignment_notes, a.truck_id,
        a.loading_location_id, a.unloading_location_id,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `);

    if (tripResult.rows.length === 0) {
      console.log('❌ No recent trips found');
      return;
    }

    const trip = tripResult.rows[0];
    console.log(`  Trip ID: ${trip.id}`);
    console.log(`  Status: ${trip.status}`);
    console.log(`  Assigned Route: ${trip.assigned_loading_location} → ${trip.assigned_unloading_location}`);
    console.log(`  Actual Route: ${trip.actual_loading_location || 'None'} → ${trip.actual_unloading_location || 'None'}`);

    // Check if dynamic assignment
    let isDynamicAssignment = false;
    try {
      const assignmentNotes = JSON.parse(trip.assignment_notes || '{}');
      isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
    } catch (error) {
      isDynamicAssignment = false;
    }
    console.log(`  Dynamic Assignment: ${isDynamicAssignment ? 'Yes' : 'No'}`);

    if (trip.status !== 'unloading_end') {
      console.log(`❌ Expected status 'unloading_end', got '${trip.status}'`);
      return;
    }

    // Step 2: Simulate scanning at Point A (assigned loading location)
    console.log('\n📱 Step 2: Simulating QR Scan at Point A');
    const locationResult = await client.query(`
      SELECT id, name, type, qr_code_data FROM locations WHERE id = $1
    `, [trip.loading_location_id]);

    if (locationResult.rows.length === 0) {
      console.log('❌ Loading location not found');
      return;
    }

    const location = locationResult.rows[0];
    console.log(`  Scanning at: ${location.name} (${location.type})`);

    // Step 3: Simulate the trip completion process
    console.log('\n⚙️ Step 3: Processing Trip Completion');
    
    // Begin transaction
    await client.query('BEGIN');

    try {
      // Calculate duration
      const now = new Date();
      let totalDuration;
      if (isDynamicAssignment && trip.unloading_start_time) {
        totalDuration = Math.round(
          (now - new Date(trip.unloading_start_time)) / (1000 * 60)
        );
      } else if (trip.loading_start_time) {
        totalDuration = Math.round(
          (now - new Date(trip.loading_start_time)) / (1000 * 60)
        );
      } else {
        totalDuration = 1;
      }

      console.log(`  Calculated Duration: ${totalDuration} minutes`);

      // Record completion location
      const locationNotes = {
        completion_location_id: location.id,
        completion_location_name: location.name,
        completion_method: 'dynamic_route_discovery',
        completion_timestamp: now.toISOString()
      };

      // Update trip to completed
      const updateResult = await client.query(`
        UPDATE trip_logs 
        SET status = $1, 
            trip_completed_time = $2, 
            total_duration_minutes = $3, 
            updated_at = $4,
            notes = COALESCE(notes::jsonb, '{}'::jsonb) || $5::jsonb
        WHERE id = $6
        RETURNING *
      `, [
        'trip_completed', 
        now, 
        totalDuration, 
        now, 
        locationNotes,
        trip.id
      ]);

      const completedTrip = updateResult.rows[0];

      // Commit transaction
      await client.query('COMMIT');

      console.log('  ✅ Trip completed successfully!');

      // Step 4: Verify the completion
      console.log('\n✅ Step 4: Verification');
      console.log(`  New Status: ${completedTrip.status}`);
      console.log(`  Completion Time: ${completedTrip.trip_completed_time}`);
      console.log(`  Total Duration: ${completedTrip.total_duration_minutes} minutes`);
      console.log(`  Completion Location: ${location.name}`);

      // Step 5: Check final trip state
      console.log('\n📋 Step 5: Final Trip State');
      const finalResult = await client.query(`
        SELECT 
          tl.id, tl.trip_number, tl.status, 
          tl.trip_completed_time, tl.total_duration_minutes,
          tl.notes,
          ll.name as assigned_loading_location,
          ul.name as assigned_unloading_location,
          all_loc.name as actual_loading_location,
          aul_loc.name as actual_unloading_location
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
        LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
        WHERE tl.id = $1
      `, [trip.id]);

      const finalTrip = finalResult.rows[0];
      console.log(`  Trip #${finalTrip.trip_number}: ${finalTrip.status}`);
      console.log(`  Assigned Route: ${finalTrip.assigned_loading_location} → ${finalTrip.assigned_unloading_location}`);
      console.log(`  Actual Route: ${finalTrip.actual_loading_location || 'None'} → ${finalTrip.actual_unloading_location || 'None'}`);
      console.log(`  Duration: ${finalTrip.total_duration_minutes} minutes`);
      console.log(`  Completed: ${finalTrip.trip_completed_time}`);

      if (finalTrip.notes) {
        try {
          const notes = JSON.parse(finalTrip.notes);
          if (notes.completion_location_name) {
            console.log(`  Completion Location: ${notes.completion_location_name}`);
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }

      console.log('\n🎉 COMPLETE WORKFLOW TEST PASSED!');
      console.log('✅ Dynamic route discovery trip completed successfully');
      console.log('✅ Trip shows correct status: trip_completed');
      console.log('✅ Duration calculated correctly from unloading start time');
      console.log('✅ Completion location recorded properly');

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('\n❌ WORKFLOW TEST FAILED!');
    console.error('Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testCompleteWorkflow().catch(console.error);
