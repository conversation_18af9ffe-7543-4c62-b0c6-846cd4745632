const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function checkTripData() {
  const client = await pool.connect();
  try {
    const result = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.loading_location_id as assignment_loading_id,
        a.unloading_location_id as assignment_unloading_id,
        ll1.name as actual_loading_name, ul1.name as actual_unloading_name,
        ll2.name as assignment_loading_name, ul2.name as assignment_unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll1 ON tl.actual_loading_location_id = ll1.id
      LEFT JOIN locations ul1 ON tl.actual_unloading_location_id = ul1.id
      LEFT JOIN locations ll2 ON a.loading_location_id = ll2.id
      LEFT JOIN locations ul2 ON a.unloading_location_id = ul2.id
      WHERE tl.id = 173
    `);
    
    console.log('Trip 173 Data:');
    const trip = result.rows[0];
    console.log('  Actual Loading ID:', trip.actual_loading_location_id);
    console.log('  Actual Unloading ID:', trip.actual_unloading_location_id);
    console.log('  Assignment Loading ID:', trip.assignment_loading_id);
    console.log('  Assignment Unloading ID:', trip.assignment_unloading_id);
    console.log('  Actual Loading Name:', trip.actual_loading_name);
    console.log('  Actual Unloading Name:', trip.actual_unloading_name);
    console.log('  Assignment Loading Name:', trip.assignment_loading_name);
    console.log('  Assignment Unloading Name:', trip.assignment_unloading_name);
    
    // Check all recent trips
    console.log('\nAll recent trips:');
    const allTrips = await client.query(`
      SELECT 
        tl.id, tl.status, dt.truck_number,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.loading_location_id as assignment_loading_id,
        a.unloading_location_id as assignment_unloading_id,
        ll2.name as assignment_loading_name, ul2.name as assignment_unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll2 ON a.loading_location_id = ll2.id
      LEFT JOIN locations ul2 ON a.unloading_location_id = ul2.id
      WHERE tl.created_at > NOW() - INTERVAL '2 hours'
      ORDER BY tl.created_at DESC
    `);
    
    allTrips.rows.forEach(trip => {
      console.log(`  Trip ${trip.id}: ${trip.truck_number} | ${trip.status} | ${trip.assignment_loading_name} → ${trip.assignment_unloading_name}`);
      console.log(`    Actual IDs: ${trip.actual_loading_location_id} → ${trip.actual_unloading_location_id}`);
      console.log(`    Assignment IDs: ${trip.assignment_loading_id} → ${trip.assignment_unloading_id}`);
    });
    
  } finally {
    client.release();
    await pool.end();
  }
}

checkTripData().catch(console.error);
