# Implementation Prompt: Multi-Location Trip Workflow (A→B→C)

## Context
You are implementing a multi-location trip workflow system for a hauling QR trip management system. The system currently supports A→B workflows (loading at Point A, unloading at Point B) and needs to be extended to support:

1. **A→B→C Extensions**: After completing A→B, truck continues to Point C for additional loading
2. **C→B→C Cycles**: Continuous cycles loading at Point C, unloading at Point B, returning to Point C
3. **Dynamic Route Discovery**: C→D→C when new destinations are discovered

## Critical Requirements

### 1. Status Field Integrity
- **MUST** maintain existing `status` field values: `'assigned'`, `'loading_start'`, `'loading_end'`, `'unloading_start'`, `'unloading_end'`, `'trip_completed'`, `'exception_pending'`, `'cancelled'`
- **MUST** preserve trip counting logic: each `'trip_completed'` status = +1 trip in Truck Trip Summary
- **NEVER** create new status values like `'extended_complete'` or `'cycle_complete'`

### 2. Data Table Display Requirements
Each trip row must show:
- **Status Column**: Standard statuses only (`🏁 Completed`, `⬇️ Unloading Started`, etc.)
- **Route Column**: Complete journey visualization (`📍Point A ↓ 📍Point B ↓ 🔄Point C`)
- **Assignment & Driver Column**: Include workflow indicators (`🔄 Extended Trip`, `🔄 Cycle Trip`)

### 3. Workflow Types
- **Standard**: Traditional A→B operations (unchanged)
- **Extended**: A→B→C (initial extension from completed A→B trip)
- **Cycle**: C→B→C (continuous cycles after extension)
- **Dynamic**: C→D→C (when new destinations discovered)

## Implementation Tasks

### Phase 1: Database Schema Enhancement

#### Task 1.1: Create Migration Script
Create `database/migrations/016_multi_location_workflow.sql`:

```sql
-- Add workflow tracking columns
ALTER TABLE trip_logs ADD COLUMN location_sequence JSONB;
ALTER TABLE trip_logs ADD COLUMN is_extended_trip BOOLEAN DEFAULT FALSE;
ALTER TABLE trip_logs ADD COLUMN workflow_type VARCHAR(50) DEFAULT 'standard';
ALTER TABLE trip_logs ADD COLUMN baseline_trip_id INTEGER;
ALTER TABLE trip_logs ADD COLUMN cycle_number INTEGER DEFAULT 1;

-- Performance indexes
CREATE INDEX idx_workflow_tracking ON trip_logs (workflow_type, is_extended_trip, cycle_number);
CREATE INDEX idx_location_sequence ON trip_logs USING GIN (location_sequence);

-- Update existing data
UPDATE trip_logs SET 
  location_sequence = jsonb_build_array(
    jsonb_build_object('name', loading_location_name, 'type', 'loading', 'confirmed', true),
    jsonb_build_object('name', unloading_location_name, 'type', 'unloading', 'confirmed', true)
  ),
  workflow_type = 'standard'
WHERE status = 'trip_completed';
```

#### Task 1.2: Add Foreign Key Constraints
```sql
ALTER TABLE trip_logs ADD CONSTRAINT fk_baseline_trip 
  FOREIGN KEY (baseline_trip_id) REFERENCES trip_logs(id);
```

### Phase 2: Server-Side Implementation

#### Task 2.1: Enhance processTruckScan() Function
In `server/routes/scanner.js`, add post-completion detection logic around line 384-456:

```javascript
// Add after trip completion logic
if (trip.status === 'trip_completed') {
  // Check if this is a post-completion loading scenario
  const isPostCompletionLoading = await checkPostCompletionLoading(
    trip.id, 
    scannedLocation.id, 
    truck.id
  );
  
  if (isPostCompletionLoading) {
    return await handlePostCompletionLoading(
      trip.id, 
      scannedLocation.id, 
      truck.id, 
      driverId
    );
  }
}
```

#### Task 2.2: Add Post-Completion Handling
```javascript
const handlePostCompletionLoading = async (completedTripId, locationId, truckId, driverId) => {
  const completedTrip = await getTripById(completedTripId);
  const location = await getLocationById(locationId);
  
  // Determine workflow type
  const workflowType = determineWorkflowType(completedTrip, location);
  
  if (workflowType === 'extended') {
    // Create A→B→C extension
    return await createExtendedTrip(completedTrip, location, truckId, driverId);
  } else if (workflowType === 'cycle') {
    // Create C→B→C cycle
    return await createCycleTrip(completedTrip, location, truckId, driverId);
  }
};
```

#### Task 2.3: Enhance AutoAssignmentCreator
In `server/utils/AutoAssignmentCreator.js`, add method:

```javascript
async handlePostCompletionLoading(completedTrip, newLocation, truckId, driverId) {
  // Create assignment for extended workflow
  const assignment = await this.createDynamicAssignment({
    truck_id: truckId,
    driver_id: driverId,
    loading_location_id: newLocation.id,
    unloading_location_id: completedTrip.unloading_location_id, // Same unloading location
    creation_method: 'post_completion_extension'
  });
  
  // Create new trip linked to baseline
  const newTrip = await this.createWorkflowTrip({
    assignment_id: assignment.id,
    truck_id: truckId,
    driver_id: driverId,
    baseline_trip_id: completedTrip.id,
    workflow_type: this.determineWorkflowType(completedTrip, newLocation),
    cycle_number: completedTrip.cycle_number + 1
  });
  
  return newTrip;
}
```

### Phase 3: Frontend Implementation

#### Task 3.1: Enhance TripsTable Component
In `client/src/pages/trips/components/TripsTable.js`, update `renderDynamicRoute()` function:

```javascript
const renderEnhancedRoute = (trip) => {
  // Parse location sequence
  const locations = trip.location_sequence || [];
  
  return (
    <div className="text-sm space-y-1">
      {locations.map((location, index) => (
        <div key={index}>
          <div className="flex items-center">
            <span className="mr-1">
              {location.confirmed ? '📍' : '❓'}
            </span>
            <span className="truncate">{location.name}</span>
            {location.type === 'loading' && (
              <span className="ml-1 text-green-600">⬆️</span>
            )}
            {location.type === 'unloading' && (
              <span className="ml-1 text-red-600">⬇️</span>
            )}
          </div>
          {index < locations.length - 1 && (
            <div className="flex items-center justify-center">
              <span className="text-secondary-400 text-xs">↓</span>
            </div>
          )}
        </div>
      ))}
      
      {/* Workflow indicator */}
      {trip.is_extended_trip && (
        <div className="flex items-center mt-1">
          <span className="text-blue-500 text-xs mr-1">🔄</span>
          <span className="text-xs text-blue-600 font-medium">
            {getWorkflowLabel(trip.workflow_type, trip.cycle_number)}
          </span>
        </div>
      )}
    </div>
  );
};
```

#### Task 3.2: Add Workflow Indicators
```javascript
const getWorkflowIndicator = (trip) => {
  if (!trip.is_extended_trip) return null;
  
  switch (trip.workflow_type) {
    case 'extended':
      return '🔄 Extended Trip';
    case 'cycle':
      return `🔄 Cycle Trip #${trip.cycle_number}`;
    case 'dynamic':
      return '🔄 Dynamic Route';
    default:
      return null;
  }
};
```

#### Task 3.3: Update Assignment & Driver Column
```javascript
// In TripsTable component, update the Assignment & Driver cell
<td className="px-4 sm:px-6 py-4">
  <div className="text-sm text-secondary-900">
    <div className="font-medium truncate">
      {trip.truck_number} • {trip.license_plate}
    </div>
    <div className="text-secondary-500 truncate">
      {trip.driver_name}
    </div>
    {/* Add workflow indicator */}
    {trip.is_extended_trip && (
      <div className="text-xs text-blue-600 font-medium mt-1">
        {getWorkflowIndicator(trip)}
      </div>
    )}
  </div>
</td>
```

### Phase 4: WebSocket Notifications

#### Task 4.1: Add Workflow Notifications
```javascript
// In WebSocket handler, add new notification types
const workflowNotifications = {
  'trip_extended': (data) => `${data.truck_number}: Trip extended to ${data.location_name}`,
  'cycle_started': (data) => `${data.truck_number}: Cycle #${data.cycle_number} started`,
  'dynamic_route': (data) => `${data.truck_number}: New route discovered to ${data.location_name}`,
  'workflow_completed': (data) => `${data.truck_number}: Workflow completed (${data.workflow_type})`
};
```

## Testing Requirements

### Unit Tests
- [ ] Workflow type detection logic
- [ ] Post-completion loading detection
- [ ] Route sequence validation
- [ ] Trip counting accuracy

### Integration Tests
- [ ] Complete A→B→C workflow
- [ ] C→B→C cycle operations
- [ ] Dynamic route discovery
- [ ] Trip summary counting

### User Acceptance Tests
- [ ] Trip Monitoring Data Table display
- [ ] Truck Trip Summary accuracy
- [ ] Real-time workflow updates
- [ ] Mobile responsive design

## Validation Checklist

### Database Integrity
- [ ] All existing trips maintain correct status values
- [ ] Location sequences are properly formatted
- [ ] Workflow relationships are correctly linked
- [ ] Performance indexes are working effectively

### Trip Counting Accuracy
- [ ] Each completed trip (A→B, A→B→C, C→B→C) shows status "Completed"
- [ ] Truck Trip Summary counts all completed trips correctly
- [ ] No duplicate counting of extended workflows
- [ ] Cycle trips are counted individually

### Display Requirements
- [ ] Route column shows complete journey (A→B→C, C→B→C)
- [ ] Status column shows standard statuses only
- [ ] Assignment & Driver column includes workflow indicators
- [ ] Progress and Duration columns calculate correctly

### Real-time Updates
- [ ] WebSocket notifications for workflow transitions
- [ ] Live status updates during route discovery
- [ ] Responsive UI updates for active workflows
- [ ] Performance under high-volume operations

## Implementation Order

1. **Database Schema** (Migration script and data updates)
2. **Server-Side Logic** (Scanner and AutoAssignmentCreator enhancements)
3. **Frontend Display** (TripsTable and workflow indicators)
4. **WebSocket Notifications** (Real-time workflow updates)
5. **Testing & Validation** (Comprehensive testing suite)

## Success Criteria

- ✅ A→B→C extensions work seamlessly
- ✅ C→B→C cycles operate continuously
- ✅ Dynamic route discovery functions properly
- ✅ Trip counting remains accurate
- ✅ All existing functionality preserved
- ✅ Performance meets requirements
- ✅ User experience is intuitive

## Risk Mitigation

- Use feature flags for gradual rollout
- Implement comprehensive rollback procedures
- Monitor performance metrics closely
- Provide thorough user training
- Maintain detailed documentation

---

**Remember**: The core principle is to extend functionality while preserving existing behavior. Every completed trip, regardless of workflow type, must count as +1 in the Truck Trip Summary.