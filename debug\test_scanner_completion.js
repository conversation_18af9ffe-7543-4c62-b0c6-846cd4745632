const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

// Mock the logger functions
const logDebug = (context, message, data) => {
  console.log(`[${context}] ${message}`, data ? JSON.stringify(data, null, 2) : '');
};

// Mock notification function
const notifyTripStatusChanged = (data) => {
  console.log('📢 Trip notification:', data);
};

// Import the actual handleUnloadingEnd function logic
async function testHandleUnloadingEnd(client, trip, assignment, location, now) {
  // Check if this is a dynamic assignment
  let isDynamicAssignment = false;
  try {
    const assignmentNotes = JSON.parse(assignment.notes || '{}');
    isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
  } catch (error) {
    isDynamicAssignment = false;
  }

  console.log(`🔄 Dynamic Assignment: ${isDynamicAssignment}`);

  // For dynamic route discovery trips, we may not have all loading steps
  if (isDynamicAssignment) {
    // Dynamic route discovery: Only require unloading steps to be completed
    // Note: For trips with status 'unloading_end', unloading_end_time may be NULL if this is the completion scan
    if (!trip.unloading_start_time) {
      throw new Error(`Cannot complete dynamic route trip: Missing required step: unloading start`);
    }
    
    logDebug('DYNAMIC_TRIP_COMPLETION', 'Completing dynamic route discovery trip', {
      trip_id: trip.id,
      assignment_id: assignment.id,
      location_name: location.name,
      location_type: location.type,
      has_loading_steps: !!(trip.loading_start_time && trip.loading_end_time),
      has_unloading_start: !!trip.unloading_start_time,
      has_unloading_end: !!trip.unloading_end_time,
      trip_status: trip.status
    });
  } else {
    // Traditional trip: Verify all required steps have been completed in the proper sequence
    if (!trip.loading_start_time || !trip.loading_end_time || 
        !trip.unloading_start_time || !trip.unloading_end_time) {
      
      // Build detailed missing steps messages
      const missingSteps = [];
      if (!trip.loading_start_time) missingSteps.push("loading start");
      if (!trip.loading_end_time) missingSteps.push("loading end");
      if (!trip.unloading_start_time) missingSteps.push("unloading start");
      if (!trip.unloading_end_time) missingSteps.push("unloading end");
      
      throw new Error(`Cannot complete trip: Missing required steps: ${missingSteps.join(", ")}`);
    }
  }

  // Verify the timestamps are in the correct sequence (only for traditional trips)
  if (!isDynamicAssignment) {
    if (trip.loading_end_time < trip.loading_start_time || 
        trip.unloading_start_time < trip.loading_end_time ||
        trip.unloading_end_time < trip.unloading_start_time) {
      throw new Error('Trip step timestamps are out of sequence. Cannot complete trip.');
    }
  }

  // Simplified approach: Allow flexible completion at any loading location
  if (location.type === 'loading') {
    // Complete trip at current loading location (flexible completion)
    logDebug('TRIP_COMPLETION', 'Completing trip at loading location', {
      trip_id: trip.id,
      assignment_id: assignment.id,
      completion_location: location.name,
      truck_id: assignment.truck_id,
      location_id: location.id,
      is_dynamic: isDynamicAssignment
    });

    // Calculate total trip duration based on available timestamps
    let totalDuration;
    if (isDynamicAssignment && trip.unloading_start_time) {
      // For dynamic routes, calculate from unloading start if loading times are missing
      totalDuration = Math.round(
        (now - new Date(trip.unloading_start_time)) / (1000 * 60)
      );
    } else if (trip.loading_start_time) {
      // Traditional calculation from loading start
      totalDuration = Math.round(
        (now - new Date(trip.loading_start_time)) / (1000 * 60)
      );
    } else {
      // Fallback: minimal duration
      totalDuration = 1;
    }

    // Record final completion location for audit purposes
    const locationNotes = {
      completion_location_id: location.id,
      completion_location_name: location.name
    };

    console.log(`📊 Calculated duration: ${totalDuration} minutes`);
    console.log(`📍 Completion location: ${location.name}`);

    // Simulate the trip completion update
    console.log('✅ Trip would be completed successfully!');
    return {
      message: `Trip completed at ${location.name} after ${totalDuration} minutes`,
      trip_log_id: trip.id,
      status: 'trip_completed',
      total_duration: totalDuration,
      completion_location: location.name
    };
  } else {
    throw new Error(`Cannot complete trip at ${location.type} location. Must return to a loading location.`);
  }
}

async function testScannerCompletion() {
  const client = await pool.connect();
  try {
    console.log('🧪 Testing Scanner Trip Completion Logic...');

    // Get the current trip and assignment
    const result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.notes as assignment_notes, a.truck_id,
        a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `);

    if (result.rows.length === 0) {
      console.log('❌ No recent trips found');
      return;
    }

    const trip = result.rows[0];
    const assignment = {
      id: trip.assignment_id,
      truck_id: trip.truck_id,
      loading_location_id: trip.loading_location_id,
      unloading_location_id: trip.unloading_location_id,
      notes: trip.assignment_notes
    };

    console.log(`📊 Testing with Trip ID: ${trip.id}, Status: ${trip.status}`);

    // Get Point A location (assigned loading location)
    const locationResult = await client.query(`
      SELECT id, name, type FROM locations WHERE id = $1
    `, [assignment.loading_location_id]);

    if (locationResult.rows.length === 0) {
      console.log('❌ Loading location not found');
      return;
    }

    const location = locationResult.rows[0];
    console.log(`📍 Testing completion at: ${location.name} (${location.type})`);

    // Test the handleUnloadingEnd logic
    const now = new Date();
    const result_completion = await testHandleUnloadingEnd(client, trip, assignment, location, now);
    
    console.log('\n🎉 SUCCESS! Trip completion test passed');
    console.log('📋 Result:', result_completion);

  } catch (error) {
    console.error('\n❌ FAILED! Trip completion test failed');
    console.error('Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testScannerCompletion().catch(console.error);
