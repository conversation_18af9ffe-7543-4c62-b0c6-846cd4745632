const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function addAutoCompletedStatus() {
  const client = await pool.connect();
  try {
    console.log('🔧 Adding auto_completed status to trip_status enum...\n');
    
    // Check current enum values
    const currentEnum = await client.query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
      ORDER BY enumsortorder
    `);
    
    console.log('Current trip_status enum values:');
    currentEnum.rows.forEach(row => console.log(`  - ${row.enumlabel}`));
    
    // Check if auto_completed already exists
    const hasAutoCompleted = currentEnum.rows.some(row => row.enumlabel === 'auto_completed');
    
    if (hasAutoCompleted) {
      console.log('\n✅ auto_completed status already exists in enum');
    } else {
      console.log('\n🔧 Adding auto_completed to trip_status enum...');
      
      // Add the new enum value
      await client.query(`
        ALTER TYPE trip_status ADD VALUE 'auto_completed'
      `);
      
      console.log('✅ Successfully added auto_completed to trip_status enum');
    }
    
    // Verify the addition
    const updatedEnum = await client.query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
      ORDER BY enumsortorder
    `);
    
    console.log('\nUpdated trip_status enum values:');
    updatedEnum.rows.forEach(row => console.log(`  - ${row.enumlabel}`));
    
    console.log('\n🎉 auto_completed status is now available for use!');
    
  } catch (error) {
    console.error('❌ Error adding auto_completed status:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

addAutoCompletedStatus();
