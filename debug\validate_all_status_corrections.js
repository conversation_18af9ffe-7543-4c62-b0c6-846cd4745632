const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function validateAllStatusCorrections() {
  const client = await pool.connect();
  try {
    console.log('🔍 Validating All Trip Status Corrections...');
    console.log('=' .repeat(60));

    // Get both Trip #1 and Trip #2 current states
    const tripsResult = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time, tl.total_duration_minutes,
        tl.unloading_duration_minutes,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.assignment_code, a.notes as assignment_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.id IN (104, 105)
      ORDER BY tl.trip_number DESC
    `);

    console.log(`\n📊 Found ${tripsResult.rows.length} trips to validate:`);

    tripsResult.rows.forEach((trip, index) => {
      console.log(`\n🚚 Trip #${trip.trip_number} (ID: ${trip.id}) Validation:`);
      console.log('=' .repeat(40));
      console.log(`   Assignment: ${trip.assignment_code}`);
      console.log(`   Current Status: ${trip.status}`);
      
      // Analyze trip progression
      const hasLoadingPhase = !!(trip.loading_start_time && trip.loading_end_time);
      const hasUnloadingPhase = !!(trip.unloading_start_time && trip.unloading_end_time);
      const hasCompletion = !!trip.trip_completed_time;
      
      console.log(`\n   📊 Phase Analysis:`);
      console.log(`      Loading Phase: ${hasLoadingPhase ? 'Complete ✅' : 'Missing ❌'}`);
      console.log(`      Unloading Phase: ${hasUnloadingPhase ? 'Complete ✅' : 'Missing ❌'}`);
      console.log(`      Trip Completed Time: ${hasCompletion ? 'Set ❌' : 'NULL ✅'}`);
      
      // Determine expected status
      let expectedStatus = 'assigned';
      let expectedReason = '';
      
      if (hasLoadingPhase && hasUnloadingPhase && hasCompletion) {
        expectedStatus = 'trip_completed';
        expectedReason = 'Fully completed trip with both phases';
      } else if (hasUnloadingPhase && !hasLoadingPhase) {
        expectedStatus = 'unloading_end';
        expectedReason = 'Dynamic route discovery - unloading only';
      } else if (hasLoadingPhase && !hasUnloadingPhase) {
        expectedStatus = 'loading_end';
        expectedReason = 'Auto-completed - loading only';
      } else if (trip.unloading_start_time && !trip.unloading_end_time) {
        expectedStatus = 'unloading_start';
        expectedReason = 'Unloading in progress';
      } else if (trip.loading_start_time && !trip.loading_end_time) {
        expectedStatus = 'loading_start';
        expectedReason = 'Loading in progress';
      }
      
      console.log(`\n   🎯 Status Validation:`);
      console.log(`      Expected Status: ${expectedStatus}`);
      console.log(`      Actual Status: ${trip.status}`);
      console.log(`      Reason: ${expectedReason}`);
      
      const statusCorrect = trip.status === expectedStatus;
      const completionTimeCorrect = !hasCompletion || (hasCompletion && expectedStatus === 'trip_completed');
      
      console.log(`\n   ✅ Validation Results:`);
      console.log(`      Status Correct: ${statusCorrect ? 'YES ✅' : 'NO ❌'}`);
      console.log(`      Completion Time Correct: ${completionTimeCorrect ? 'YES ✅' : 'NO ❌'}`);
      
      // Check assignment type
      let assignmentType = 'unknown';
      if (trip.assignment_notes) {
        try {
          const assignmentNotes = JSON.parse(trip.assignment_notes);
          assignmentType = assignmentNotes.creation_method || 'unknown';
        } catch (e) {
          assignmentType = 'parse_error';
        }
      }
      
      console.log(`      Assignment Type: ${assignmentType}`);
      
      // Duration validation
      console.log(`\n   ⏱️ Duration Analysis:`);
      console.log(`      Total Duration: ${trip.total_duration_minutes || 'NULL'} minutes`);
      console.log(`      Unloading Duration: ${trip.unloading_duration_minutes || 'NULL'} minutes`);
      
      if (expectedStatus === 'loading_end' && hasLoadingPhase) {
        const expectedDuration = Math.round(
          (new Date(trip.loading_end_time) - new Date(trip.loading_start_time)) / (1000 * 60)
        );
        console.log(`      Expected Duration: ${expectedDuration} minutes (loading phase)`);
        const durationCorrect = Math.abs((trip.total_duration_minutes || 0) - expectedDuration) <= 1;
        console.log(`      Duration Correct: ${durationCorrect ? 'YES ✅' : 'NO ❌'}`);
      } else if (expectedStatus === 'unloading_end' && hasUnloadingPhase) {
        const expectedDuration = Math.round(
          (new Date(trip.unloading_end_time) - new Date(trip.unloading_start_time)) / (1000 * 60)
        );
        console.log(`      Expected Duration: ${expectedDuration} minutes (unloading phase)`);
        const durationCorrect = Math.abs((trip.total_duration_minutes || 0) - expectedDuration) <= 1;
        console.log(`      Duration Correct: ${durationCorrect ? 'YES ✅' : 'NO ❌'}`);
      }
      
      // Overall validation
      const overallCorrect = statusCorrect && completionTimeCorrect;
      console.log(`\n   🎯 Overall Validation: ${overallCorrect ? '✅ PASSED' : '❌ FAILED'}`);
      
      // Specific validations for each trip
      if (trip.id === 104) {
        console.log(`\n   📋 Trip #2 Specific Validation:`);
        console.log(`      Should show "Loading Complete" in dashboard: ${trip.status === 'loading_end' ? 'YES ✅' : 'NO ❌'}`);
        console.log(`      Auto-completion preserved loading phase: ${hasLoadingPhase && !hasUnloadingPhase ? 'YES ✅' : 'NO ❌'}`);
      } else if (trip.id === 105) {
        console.log(`\n   📋 Trip #1 Specific Validation:`);
        console.log(`      Should show "Unloading Complete" in dashboard: ${trip.status === 'unloading_end' ? 'YES ✅' : 'NO ❌'}`);
        console.log(`      Dynamic route discovery unloading only: ${!hasLoadingPhase && hasUnloadingPhase ? 'YES ✅' : 'NO ❌'}`);
      }
    });

    // Summary validation
    console.log('\n🎯 Overall Validation Summary:');
    console.log('=' .repeat(50));
    
    const trip2 = tripsResult.rows.find(t => t.id === 104);
    const trip1 = tripsResult.rows.find(t => t.id === 105);
    
    const validations = [
      {
        name: 'Trip #2 status is loading_end',
        passed: trip2?.status === 'loading_end',
        description: 'Auto-completed trip preserves loading phase status'
      },
      {
        name: 'Trip #2 has no trip_completed_time',
        passed: !trip2?.trip_completed_time,
        description: 'Partial trips should not have completion timestamp'
      },
      {
        name: 'Trip #1 status is unloading_end',
        passed: trip1?.status === 'unloading_end',
        description: 'Dynamic route discovery preserves unloading phase status'
      },
      {
        name: 'Trip #1 has no trip_completed_time',
        passed: !trip1?.trip_completed_time,
        description: 'Partial trips should not have completion timestamp'
      },
      {
        name: 'Trip #2 has loading phase only',
        passed: !!(trip2?.loading_start_time && trip2?.loading_end_time && !trip2?.unloading_start_time),
        description: 'Auto-completed trip completed loading but not unloading'
      },
      {
        name: 'Trip #1 has unloading phase only',
        passed: !!(trip1?.unloading_start_time && trip1?.unloading_end_time && !trip1?.loading_start_time),
        description: 'Dynamic route discovery completed unloading but not loading'
      }
    ];
    
    let passedValidations = 0;
    validations.forEach((validation, index) => {
      console.log(`\n${index + 1}. ${validation.name}:`);
      console.log(`   Result: ${validation.passed ? '✅ PASSED' : '❌ FAILED'}`);
      console.log(`   Description: ${validation.description}`);
      if (validation.passed) passedValidations++;
    });
    
    console.log(`\n📊 Validation Score: ${passedValidations}/${validations.length} checks passed`);
    
    if (passedValidations === validations.length) {
      console.log('\n🎉 ALL VALIDATIONS PASSED!');
      console.log('✅ Trip status corrections are working correctly');
      console.log('✅ Database reflects actual trip progression');
      console.log('✅ Auto-completion logic preserves actual phases');
      console.log('✅ Trip Monitoring Dashboard will show accurate statuses');
      console.log('✅ No more false trip_completed statuses');
    } else {
      console.log('\n⚠️ Some validations failed - please review');
    }

  } catch (error) {
    console.error('❌ Error validating status corrections:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

validateAllStatusCorrections().catch(console.error);
