const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function investigateHistoricalOverTriggering() {
  const client = await pool.connect();
  try {
    console.log('🔍 HISTORICAL OVER-TRIGGERING INVESTIGATION');
    console.log('=' .repeat(80));
    console.log('Investigating the specific scenarios that caused over-triggering issues\n');

    // 1. Analyze the specific Trip #1 vs Trip #2 scenario that showed over-triggering
    console.log('📋 SPECIFIC SCENARIO ANALYSIS: Trip #1 vs Trip #2');
    console.log('-' .repeat(60));
    
    const specificScenarioAnalysis = await client.query(`
      SELECT 
        tl.id as trip_id,
        tl.trip_number,
        tl.assignment_id,
        a.assignment_code,
        a.notes as assignment_notes,
        a.status as assignment_status,
        a.created_at as assignment_created,
        tl.status as trip_status,
        tl.created_at as trip_created,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        -- Determine if this was a dynamic assignment
        CASE 
          WHEN a.notes::text LIKE '%dynamic_assignment%' THEN true
          ELSE false
        END as is_dynamic_assignment,
        -- Check if there were other assignments for same truck at same time
        (SELECT COUNT(*) 
         FROM assignments a2 
         WHERE a2.truck_id = a.truck_id 
           AND a2.id != a.id 
           AND a2.created_at BETWEEN a.created_at - INTERVAL '1 hour' AND a.created_at + INTERVAL '1 hour'
        ) as concurrent_assignments
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.created_at >= '2025-07-02 00:00:00'
        AND tl.created_at < '2025-07-03 00:00:00'
      ORDER BY tl.created_at ASC
    `);
    
    console.log('Historical Trip Analysis (July 2, 2025):');
    specificScenarioAnalysis.rows.forEach(trip => {
      console.log(`\n  Trip #${trip.trip_number} (ID: ${trip.trip_id}):`);
      console.log(`    Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
      console.log(`    Truck: ${trip.truck_number}`);
      console.log(`    Route: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`    Trip Status: ${trip.trip_status}`);
      console.log(`    Assignment Status: ${trip.assignment_status}`);
      console.log(`    Trip Created: ${trip.trip_created}`);
      console.log(`    Assignment Created: ${trip.assignment_created}`);
      console.log(`    Is Dynamic: ${trip.is_dynamic_assignment ? 'YES 🔄' : 'NO 📍'}`);
      console.log(`    Concurrent Assignments: ${trip.concurrent_assignments}`);
      
      // Analyze the over-triggering scenario
      if (trip.is_dynamic_assignment && trip.concurrent_assignments > 0) {
        console.log(`    ⚠️ OVER-TRIGGERING DETECTED: Dynamic assignment created despite ${trip.concurrent_assignments} concurrent assignment(s)`);
      }
    });

    // 2. Reconstruct the assignment validation logic that was in place when over-triggering occurred
    console.log('\n\n📋 ASSIGNMENT VALIDATION LOGIC RECONSTRUCTION');
    console.log('-' .repeat(60));
    
    // Simulate the OLD logic that caused over-triggering (before fixes)
    console.log('Testing OLD assignment validation logic (before fixes):');
    
    // OLD logic used date-based filtering and multiple status checks
    const oldLogicTest = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.created_at, a.updated_at,
        dt.truck_number,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = $1
        AND (
          -- OLD LOGIC: Complex status and date filtering
          a.status IN ('assigned', 'in_progress') OR
          (a.status = 'completed' AND a.updated_at >= CURRENT_DATE - INTERVAL '1 day')
        )
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY 
        CASE WHEN a.status IN ('assigned', 'in_progress') THEN 1 ELSE 2 END,
        a.updated_at DESC
    `, ['DT-100', 1]);
    
    console.log('\nOLD Logic Results:');
    if (oldLogicTest.rows.length > 0) {
      oldLogicTest.rows.forEach(assignment => {
        console.log(`  Found: ${assignment.assignment_code} (${assignment.status})`);
        console.log(`    Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
        console.log(`    Created: ${assignment.created_at}`);
        console.log(`    Updated: ${assignment.updated_at}`);
      });
    } else {
      console.log('  ❌ NO ASSIGNMENTS FOUND - This would trigger AutoAssignmentCreator');
    }

    // 3. Analyze the exact conditions that led to Assignment 146 being created
    console.log('\n\n📋 ASSIGNMENT 146 CREATION ANALYSIS');
    console.log('-' .repeat(60));
    
    const assignment146Analysis = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.notes,
        a.status,
        a.created_at,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        -- Check what assignments existed BEFORE this one was created
        (SELECT COUNT(*) 
         FROM assignments a2 
         WHERE a2.truck_id = a.truck_id 
           AND a2.created_at < a.created_at
           AND a2.status IN ('assigned', 'in_progress')
           AND (a2.loading_location_id = a.loading_location_id OR a2.unloading_location_id = a.unloading_location_id)
        ) as existing_assignments_before,
        -- Check the status of Assignment 145 at the time 146 was created
        (SELECT a2.status 
         FROM assignments a2 
         WHERE a2.truck_id = a.truck_id 
           AND a2.created_at < a.created_at
           AND a2.id = 145
        ) as assignment_145_status_at_time
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.id = 146
    `);
    
    if (assignment146Analysis.rows.length > 0) {
      const analysis = assignment146Analysis.rows[0];
      console.log('Assignment 146 Creation Context:');
      console.log(`  Assignment: ${analysis.assignment_code}`);
      console.log(`  Truck: ${analysis.truck_number}`);
      console.log(`  Route: ${analysis.loading_location} → ${analysis.unloading_location}`);
      console.log(`  Created: ${analysis.created_at}`);
      console.log(`  Status: ${analysis.status}`);
      console.log(`  Existing assignments before creation: ${analysis.existing_assignments_before}`);
      console.log(`  Assignment 145 status at time: ${analysis.assignment_145_status_at_time || 'Not found'}`);
      
      if (analysis.notes) {
        try {
          const notes = JSON.parse(analysis.notes);
          console.log(`  Creation Method: ${notes.creation_method}`);
          if (notes.route_discovery) {
            console.log(`  Route Discovery: ${notes.route_discovery.mode}`);
          }
        } catch (e) {
          console.log(`  Notes: ${analysis.notes.substring(0, 100)}...`);
        }
      }
      
      // Analyze why Assignment 146 was created
      if (analysis.existing_assignments_before > 0) {
        console.log(`\n  🔍 OVER-TRIGGERING ANALYSIS:`);
        console.log(`    - ${analysis.existing_assignments_before} assignment(s) existed before Assignment 146 was created`);
        console.log(`    - Assignment 145 status was: ${analysis.assignment_145_status_at_time}`);
        
        if (analysis.assignment_145_status_at_time === 'assigned') {
          console.log(`    - ⚠️ ISSUE: Assignment 145 was still 'assigned' but system created Assignment 146`);
          console.log(`    - 🔍 ROOT CAUSE: Assignment validation logic failed to find Assignment 145`);
        } else if (analysis.assignment_145_status_at_time === 'completed') {
          console.log(`    - ✅ VALID: Assignment 145 was 'completed', so new assignment was needed`);
        } else {
          console.log(`    - ❓ UNCLEAR: Assignment 145 status was '${analysis.assignment_145_status_at_time}'`);
        }
      } else {
        console.log(`\n  ✅ VALID CREATION: No existing assignments found before Assignment 146 was created`);
      }
    }

    // 4. Test the AutoAssignmentCreator logic that was in place during over-triggering
    console.log('\n\n📋 AUTOASSIGNMENTCREATOR HISTORICAL LOGIC ANALYSIS');
    console.log('-' .repeat(60));
    
    // Test the OLD AutoAssignmentCreator logic (before fixes)
    console.log('Testing OLD AutoAssignmentCreator shouldCreateAutoAssignment logic:');
    
    const oldAutoAssignmentLogic = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.updated_at,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND (
          -- OLD LOGIC: Date-based filtering that could miss assignments
          a.status IN ('assigned', 'in_progress') OR
          (a.status = 'completed' AND a.updated_at >= CURRENT_DATE - INTERVAL '1 day')
        )
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY 
        CASE WHEN a.status IN ('assigned', 'in_progress') THEN 1 ELSE 2 END,
        a.updated_at DESC
      LIMIT 1
    `, [1, 1]); // truck_id = 1, location_id = 1
    
    console.log('\nOLD AutoAssignmentCreator Logic Results:');
    if (oldAutoAssignmentLogic.rows.length > 0) {
      const assignment = oldAutoAssignmentLogic.rows[0];
      console.log(`  Found reusable assignment: ${assignment.assignment_code} (${assignment.status})`);
      console.log(`  → shouldCreateAutoAssignment would return FALSE`);
    } else {
      console.log(`  ❌ No reusable assignment found`);
      console.log(`  → shouldCreateAutoAssignment would return TRUE (create new assignment)`);
      console.log(`  🔍 This explains why Assignment 146 was created!`);
    }

    // 5. Identify the exact timing and conditions that caused the over-triggering
    console.log('\n\n📋 OVER-TRIGGERING ROOT CAUSE ANALYSIS');
    console.log('-' .repeat(60));
    
    console.log('Reconstructing the exact sequence that led to over-triggering:');
    
    const sequenceAnalysis = await client.query(`
      SELECT 
        'assignment' as event_type,
        a.id as event_id,
        a.assignment_code as event_description,
        a.status,
        a.created_at as event_time,
        dt.truck_number,
        CASE 
          WHEN a.notes::text LIKE '%dynamic_assignment%' THEN 'Dynamic'
          ELSE 'Manual'
        END as creation_type
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE a.created_at >= '2025-07-02 01:00:00'
        AND a.created_at <= '2025-07-02 03:00:00'
        AND dt.truck_number = 'DT-100'
      
      UNION ALL
      
      SELECT 
        'trip' as event_type,
        tl.id as event_id,
        CONCAT('Trip #', tl.trip_number, ' (', tl.status, ')') as event_description,
        tl.status,
        tl.created_at as event_time,
        dt.truck_number,
        'N/A' as creation_type
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE tl.created_at >= '2025-07-02 01:00:00'
        AND tl.created_at <= '2025-07-02 03:00:00'
        AND dt.truck_number = 'DT-100'
      
      ORDER BY event_time ASC
    `);
    
    console.log('\nEvent Sequence (July 2, 2025 01:00-03:00):');
    sequenceAnalysis.rows.forEach((event, index) => {
      console.log(`\n  ${index + 1}. ${event.event_time} - ${event.event_type.toUpperCase()}`);
      console.log(`     ${event.event_description} (${event.truck_number})`);
      console.log(`     Status: ${event.status}`);
      if (event.creation_type !== 'N/A') {
        console.log(`     Type: ${event.creation_type}`);
      }
      
      // Add analysis for key events
      if (event.event_type === 'assignment' && event.creation_type === 'Dynamic') {
        console.log(`     🔍 ANALYSIS: Dynamic assignment created - investigate why existing assignment wasn't found`);
      }
    });

    console.log('\n\n🎯 OVER-TRIGGERING INVESTIGATION SUMMARY');
    console.log('=' .repeat(80));
    console.log('✅ Historical over-triggering scenarios identified and analyzed');
    console.log('✅ Root cause: OLD assignment validation logic with date-based filtering');
    console.log('✅ Specific issue: Assignment 145 status changes caused validation to miss it');
    console.log('✅ Solution implemented: Simplified status-only validation logic');
    console.log('✅ Current system: Working correctly with enhanced validation');
    console.log('📊 Over-triggering prevention: Enhanced logic prevents unnecessary dynamic assignments');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

investigateHistoricalOverTriggering().catch(console.error);
