const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testABCWorkflow() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Testing A→B→C Workflow Implementation\n');
    
    // Step 1: Create test data for A→B→C scenario
    console.log('📋 Step 1: Setting up test scenario...');
    
    // Get a truck and locations
    const truck = await client.query(`
      SELECT id, truck_number, license_plate 
      FROM dump_trucks 
      WHERE status = 'active' 
      LIMIT 1
    `);
    
    if (truck.rows.length === 0) {
      console.log('❌ No active trucks found');
      return;
    }
    
    const truckData = truck.rows[0];
    console.log(`  Using truck: ${truckData.truck_number} (${truckData.license_plate})`);
    
    // Get three different locations: A (loading), B (unloading), C (loading)
    const locations = await client.query(`
      SELECT id, name, type, location_code
      FROM locations 
      WHERE type IN ('loading', 'unloading')
      ORDER BY type, name
      LIMIT 10
    `);
    
    const loadingLocations = locations.rows.filter(l => l.type === 'loading');
    const unloadingLocations = locations.rows.filter(l => l.type === 'unloading');
    
    if (loadingLocations.length < 2 || unloadingLocations.length < 1) {
      console.log('❌ Insufficient locations for test');
      return;
    }
    
    const locationA = loadingLocations[0];  // Original loading
    const locationB = unloadingLocations[0]; // Unloading
    const locationC = loadingLocations[1];  // New loading for extension
    
    console.log(`  Location A (loading): ${locationA.name}`);
    console.log(`  Location B (unloading): ${locationB.name}`);
    console.log(`  Location C (loading): ${locationC.name}`);
    
    // Step 2: Create A→B assignment and complete the trip
    console.log('\n📋 Step 2: Creating and completing A→B trip...');
    
    // Check for existing assignment and delete if needed
    await client.query(`
      DELETE FROM assignments
      WHERE truck_id = $1
        AND loading_location_id = $2
        AND unloading_location_id = $3
        AND assigned_date = CURRENT_DATE
    `, [truckData.id, locationA.id, locationB.id]);

    // Create assignment A→B
    const assignment = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
        assigned_date, status, priority, expected_loads_per_day, notes
      )
      SELECT
        'TEST-AB-' || EXTRACT(EPOCH FROM NOW())::text || '-' || RANDOM()::text,
        $1,
        (SELECT id FROM drivers LIMIT 1),
        $2, $3,
        CURRENT_DATE, 'assigned', 'normal', 1,
        '{"test_scenario": "A_to_B_baseline"}'
      RETURNING *
    `, [truckData.id, locationA.id, locationB.id]);
    
    const assignmentData = assignment.rows[0];
    console.log(`  Created assignment: ${assignmentData.assignment_code}`);
    
    // Create and complete A→B trip
    const completedTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, 
        loading_start_time, loading_end_time, 
        unloading_start_time, unloading_end_time, trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        workflow_type, location_sequence
      )
      VALUES (
        $1, 1, 'trip_completed',
        NOW() - INTERVAL '2 hours',
        NOW() - INTERVAL '1 hour 45 minutes',
        NOW() - INTERVAL '1 hour 30 minutes', 
        NOW() - INTERVAL '1 hour 15 minutes',
        NOW() - INTERVAL '1 hour',
        $2, $3,
        'standard',
        $4
      )
      RETURNING *
    `, [
      assignmentData.id, 
      locationA.id, 
      locationB.id,
      JSON.stringify([
        { name: locationA.name, type: 'loading', confirmed: true, location_id: locationA.id },
        { name: locationB.name, type: 'unloading', confirmed: true, location_id: locationB.id }
      ])
    ]);
    
    const baselineTrip = completedTrip.rows[0];
    console.log(`  Completed A→B trip: ${baselineTrip.id} (status: ${baselineTrip.status})`);
    
    // Step 3: Simulate truck scanning at location C
    console.log('\n📋 Step 3: Simulating truck scan at location C...');
    console.log(`  Truck ${truckData.truck_number} scanning at ${locationC.name} (${locationC.type})`);
    
    // Check current state before scan
    const beforeScan = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as completed_trips,
        COUNT(CASE WHEN workflow_type = 'standard' THEN 1 END) as standard_trips,
        COUNT(CASE WHEN workflow_type = 'extended' THEN 1 END) as extended_trips
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
    `, [truckData.id]);
    
    console.log('  Before scan state:');
    console.log(`    Total trips: ${beforeScan.rows[0].total_trips}`);
    console.log(`    Completed trips: ${beforeScan.rows[0].completed_trips}`);
    console.log(`    Standard trips: ${beforeScan.rows[0].standard_trips}`);
    console.log(`    Extended trips: ${beforeScan.rows[0].extended_trips}`);
    
    // Import and test the scanner function
    const { processTruckScan } = require('./routes/scanner');
    
    const scanResult = await processTruckScan(
      client,
      { type: 'truck', id: truckData.truck_number }, // QR data
      { type: 'location', id: locationC.location_code }, // Location scan data
      1, // userId
      '127.0.0.1', // ipAddress
      'test-agent' // userAgent
    );
    
    console.log('\n📊 Scan Result:');
    console.log(`  Success: ${scanResult.success || 'N/A'}`);
    console.log(`  Message: ${scanResult.message}`);
    console.log(`  Action: ${scanResult.action || 'N/A'}`);
    console.log(`  Workflow Type: ${scanResult.workflow_type || 'N/A'}`);
    console.log(`  New Trip ID: ${scanResult.trip?.id || 'N/A'}`);
    
    // Step 4: Verify the results
    console.log('\n📋 Step 4: Verifying workflow results...');
    
    const afterScan = await client.query(`
      SELECT 
        tl.id, tl.status, tl.workflow_type, tl.baseline_trip_id, tl.is_extended_trip,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        ll.name as loading_name, ul.name as unloading_name,
        a.assignment_code
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON tl.actual_loading_location_id = ll.id
      LEFT JOIN locations ul ON tl.actual_unloading_location_id = ul.id
      WHERE a.truck_id = $1
      ORDER BY tl.created_at DESC
    `, [truckData.id]);
    
    console.log('\n  All trips for this truck:');
    afterScan.rows.forEach((trip, index) => {
      const indicators = [];
      if (trip.is_extended_trip) indicators.push('🔄 Extended');
      if (trip.baseline_trip_id) indicators.push(`📎 Baseline: ${trip.baseline_trip_id}`);
      
      console.log(`    ${index + 1}. Trip ${trip.id}: ${trip.status} | ${trip.workflow_type} | ${trip.loading_name} → ${trip.unloading_name} | ${indicators.join(', ')}`);
    });
    
    // Verify expectations
    const expectedResults = {
      shouldHaveBaselineTrip: true,
      shouldHaveExtendedTrip: true,
      baselineTripShouldBeCompleted: true,
      extendedTripShouldBeActive: true,
      shouldPreserveOriginalRoute: true
    };
    
    console.log('\n✅ Verification Results:');
    
    const baselineTripExists = afterScan.rows.find(t => t.id === baselineTrip.id);
    const extendedTripExists = afterScan.rows.find(t => t.workflow_type === 'extended');
    
    console.log(`  ✅ Baseline trip preserved: ${!!baselineTripExists}`);
    console.log(`  ✅ Baseline trip still completed: ${baselineTripExists?.status === 'trip_completed'}`);
    console.log(`  ✅ Extended trip created: ${!!extendedTripExists}`);
    console.log(`  ✅ Extended trip links to baseline: ${extendedTripExists?.baseline_trip_id === baselineTrip.id}`);
    console.log(`  ✅ Original route preserved: ${baselineTripExists?.loading_name === locationA.name && baselineTripExists?.unloading_name === locationB.name}`);
    
    if (extendedTripExists) {
      console.log(`  ✅ Extended trip route: ${extendedTripExists.loading_name} → ${extendedTripExists.unloading_name}`);
      console.log(`  ✅ Expected route: ${locationC.name} → ${locationB.name}`);
      console.log(`  ✅ Route matches expectation: ${extendedTripExists.loading_name === locationC.name && extendedTripExists.unloading_name === locationB.name}`);
    }
    
    console.log('\n🎉 A→B→C Workflow Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testABCWorkflow().catch(console.error);
