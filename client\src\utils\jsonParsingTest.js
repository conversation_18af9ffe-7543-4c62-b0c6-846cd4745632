/**
 * JSON Parsing Test Utility
 * 
 * This utility tests various edge cases for JSON parsing that might occur
 * in the location_sequence field to prevent runtime errors.
 */

export const testLocationSequenceParsing = () => {
  console.log('🧪 Testing location_sequence parsing edge cases...');

  const testCases = [
    // Valid cases
    { name: 'Valid JSON array', value: '[{"name":"A","type":"loading"}]', shouldParse: true },
    { name: 'Valid JSON object', value: '{"name":"A","type":"loading"}', shouldParse: true },
    { name: 'Already parsed array', value: [{"name":"A","type":"loading"}], shouldParse: true },
    { name: 'Already parsed object', value: {"name":"A","type":"loading"}, shouldParse: false },
    
    // Invalid cases that caused the error
    { name: 'Object toString', value: '[object Object]', shouldParse: false },
    { name: 'Empty string', value: '', shouldParse: false },
    { name: 'Null', value: null, shouldParse: false },
    { name: 'Undefined', value: undefined, shouldParse: false },
    { name: 'Invalid JSON', value: '{invalid json}', shouldParse: false },
    { name: 'Plain string', value: 'not json', shouldParse: false },
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach(testCase => {
    try {
      const result = safeParseLocationSequence(testCase.value);
      const actuallyParsed = result !== null;
      
      if (actuallyParsed === testCase.shouldParse) {
        console.log(`✅ ${testCase.name}: PASS`);
        passed++;
      } else {
        console.log(`❌ ${testCase.name}: FAIL (expected ${testCase.shouldParse}, got ${actuallyParsed})`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${testCase.name}: ERROR - ${error.message}`);
      failed++;
    }
  });

  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  return { passed, failed };
};

// Safe parsing function that matches the implementation in TripsTable
export const safeParseLocationSequence = (locationSequence) => {
  if (!locationSequence) {
    return null;
  }

  try {
    // Handle both string and object cases
    if (typeof locationSequence === 'string') {
      // Only parse if it's a non-empty string that looks like JSON
      const trimmed = locationSequence.trim();
      if (trimmed && (trimmed.startsWith('[') || trimmed.startsWith('{'))) {
        return JSON.parse(trimmed);
      }
      return null;
    } else if (typeof locationSequence === 'object' && Array.isArray(locationSequence)) {
      return locationSequence;
    }
    return null;
  } catch (error) {
    console.warn('Failed to parse location_sequence:', error);
    return null;
  }
};

// Test function for assignment notes parsing
export const safeParseAssignmentNotes = (assignmentNotes) => {
  if (!assignmentNotes) {
    return null;
  }

  try {
    const notes = typeof assignmentNotes === 'string' 
      ? JSON.parse(assignmentNotes) 
      : assignmentNotes;
    return notes;
  } catch (error) {
    console.warn('Failed to parse assignment_notes:', error);
    return null;
  }
};

// Export for use in components
export default {
  testLocationSequenceParsing,
  safeParseLocationSequence,
  safeParseAssignmentNotes
};
