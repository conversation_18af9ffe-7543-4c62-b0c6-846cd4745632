const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testDynamicDetection() {
  const client = await pool.connect();
  try {
    console.log('🔍 Testing Dynamic Assignment Detection...');

    // Get Trip #1 and its assignment data exactly as scanner.js would
    const result = await client.query(`
      SELECT 
        tl.id as trip_id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        a.id as assignment_id, a.notes as assignment_notes,
        a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.id = 105
    `);

    if (result.rows.length === 0) {
      console.log('❌ Trip #1 not found');
      return;
    }

    const data = result.rows[0];
    console.log('\n📊 Raw Data from Database:');
    console.log(`Trip ID: ${data.trip_id}`);
    console.log(`Assignment ID: ${data.assignment_id}`);
    console.log(`Assignment Notes Type: ${typeof data.assignment_notes}`);
    console.log(`Assignment Notes:`, data.assignment_notes);
    console.log('');

    // Test the exact logic from handleUnloadingEnd
    console.log('🧪 Testing Dynamic Assignment Detection Logic:');
    let isDynamicAssignment = false;
    try {
      console.log('1. Parsing assignment notes...');
      const assignmentNotes = JSON.parse(data.assignment_notes || '{}');
      console.log('   Parsed notes:', assignmentNotes);
      
      console.log('2. Checking creation_method...');
      console.log(`   creation_method: "${assignmentNotes.creation_method}"`);
      
      isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
      console.log(`   isDynamicAssignment: ${isDynamicAssignment}`);
      
    } catch (error) {
      console.log(`   ❌ Parse error: ${error.message}`);
      isDynamicAssignment = false;
    }

    console.log(`\n🎯 Final Result: isDynamicAssignment = ${isDynamicAssignment}`);

    // Test the validation logic
    console.log('\n🔍 Testing Validation Logic:');
    if (isDynamicAssignment) {
      console.log('✅ Dynamic assignment detected - using dynamic validation');
      if (!data.unloading_start_time) {
        console.log('❌ Would fail: Missing unloading start');
      } else {
        console.log('✅ Would pass: Has unloading start time');
      }
    } else {
      console.log('❌ Dynamic assignment NOT detected - using traditional validation');
      const missingSteps = [];
      if (!data.loading_start_time) missingSteps.push("loading start");
      if (!data.loading_end_time) missingSteps.push("loading end");
      if (!data.unloading_start_time) missingSteps.push("unloading start");
      if (!data.unloading_end_time) missingSteps.push("unloading end");
      
      if (missingSteps.length > 0) {
        console.log(`❌ Would fail: Missing required steps: ${missingSteps.join(", ")}`);
        console.log('   This is the error you\'re seeing!');
      } else {
        console.log('✅ Would pass: All steps present');
      }
    }

    // Check if the issue is in how scanner.js gets the assignment data
    console.log('\n🔍 Checking How Scanner.js Gets Assignment Data:');
    
    // Simulate the query that scanner.js uses in processTruckScan
    const scannerQuery = await client.query(`
      SELECT 
        tl.*,
        a.id as assignment_id,
        a.assignment_code,
        a.truck_id,
        a.driver_id,
        a.loading_location_id,
        a.unloading_location_id,
        a.notes as assignment_notes,
        a.created_at as assignment_created_at
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.id = 105
    `);

    if (scannerQuery.rows.length > 0) {
      const scannerData = scannerQuery.rows[0];
      console.log('📊 Scanner.js Query Result:');
      console.log(`Assignment Notes: ${typeof scannerData.assignment_notes}`);
      console.log('Assignment Notes Content:', scannerData.assignment_notes);
      
      // Test with scanner data
      let scannerIsDynamic = false;
      try {
        const scannerNotes = JSON.parse(scannerData.assignment_notes || '{}');
        scannerIsDynamic = scannerNotes.creation_method === 'dynamic_assignment';
        console.log(`Scanner Dynamic Detection: ${scannerIsDynamic}`);
      } catch (e) {
        console.log(`Scanner Parse Error: ${e.message}`);
      }
    }

    // Potential fix if detection is failing
    if (!isDynamicAssignment) {
      console.log('\n🔧 Potential Fix Needed:');
      console.log('The dynamic assignment detection is failing.');
      console.log('Possible causes:');
      console.log('1. Assignment notes are not being parsed correctly');
      console.log('2. The creation_method field is missing or different');
      console.log('3. The assignment data is not being retrieved properly in scanner.js');
      
      // Check if we can detect it another way
      console.log('\n🔍 Alternative Detection Methods:');
      
      // Check assignment code pattern
      if (data.assignment_notes) {
        try {
          const notes = JSON.parse(data.assignment_notes);
          console.log('Available fields in assignment notes:');
          Object.keys(notes).forEach(key => {
            console.log(`  - ${key}: ${notes[key]}`);
          });
        } catch (e) {
          console.log('Cannot parse assignment notes for field inspection');
        }
      }
    }

  } catch (error) {
    console.error('❌ Error testing dynamic detection:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testDynamicDetection().catch(console.error);
