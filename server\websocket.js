const WebSocket = require('ws');

let wss = null;
const clients = new Map(); // Store client connections with metadata

// Initialize WebSocket server
const initializeWebSocket = (server) => {
  wss = new WebSocket.Server({ server });

  wss.on('connection', (ws, req) => {
    const clientId = generateClientId();
    
    // Store client with metadata
    clients.set(clientId, {
      ws,
      userId: null,
      role: null,
      lastActivity: Date.now()
    });

    console.log(`WebSocket client connected: ${clientId}`);

    // Handle authentication message
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        if (data.type === 'auth') {
          const client = clients.get(clientId);
          if (client) {
            client.userId = data.userId;
            client.role = data.role;
            client.lastActivity = Date.now();
            
            // Send authentication confirmation
            ws.send(JSON.stringify({
              type: 'auth_success',
              message: 'Authenticated successfully'
            }));
            
            console.log(`Client ${clientId} authenticated as user ${data.userId} with role ${data.role}`);
          }
        }
      } catch (error) {
        console.error('WebSocket message parse error:', error);
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      clients.delete(clientId);
      console.log(`WebSocket client disconnected: ${clientId}`);
    });

    // Handle connection errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for client ${clientId}:`, error);
      clients.delete(clientId);
    });

    // Send initial welcome message
    ws.send(JSON.stringify({
      type: 'welcome',
      clientId,
      message: 'Connected to Hauling System WebSocket'
    }));
  });

  // Cleanup inactive connections every 30 seconds
  setInterval(() => {
    const now = Date.now();
    const INACTIVE_THRESHOLD = 5 * 60 * 1000; // 5 minutes

    for (const [clientId, client] of clients.entries()) {
      if (now - client.lastActivity > INACTIVE_THRESHOLD) {
        console.log(`Removing inactive client: ${clientId}`);
        client.ws.terminate();
        clients.delete(clientId);
      }
    }
  }, 30000);

  console.log('WebSocket server initialized');
  return wss;
};

// Generate unique client ID
const generateClientId = () => {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
};

// Broadcast to all authenticated clients
const broadcast = (message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.userId) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Send to specific users by role
const sendToRole = (role, message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.role === role) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Send to specific user
const sendToUser = (userId, message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.userId === userId) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Notification functions for specific events
const notifyExceptionCreated = (exception) => {
  // Enhanced notification for hybrid exceptions
  const isAdaptive = exception.is_adaptive || false;
  const hasInsights = exception.adaptation_insights || false;

  let title = 'New Exception Requires Approval';
  let message = `Route deviation detected: ${exception.description || exception.exception_description}`;

  if (isAdaptive && hasInsights) {
    title = 'Smart Exception with AI Insights';
    message = `Route deviation detected with pattern analysis: ${exception.description || exception.exception_description}`;
  }

  const notificationData = {
    type: 'exception_created',
    title,
    message,
    data: {
      ...exception,
      is_adaptive: isAdaptive,
      has_insights: hasInsights
    },
    timestamp: new Date().toISOString(),
    priority: exception.severity || 'medium'
  };

  // Notify all admins and supervisors
  sendToRole('admin', notificationData);
  sendToRole('supervisor', notificationData);
};

const notifyExceptionUpdated = (exception, decision) => {
  // Enhanced notification for hybrid exception updates
  const description = exception.exception_description || exception.description || 'route deviation';
  const isAdaptive = exception.is_adaptive || false;
  const autoApproved = exception.auto_approved || false;

  let title = `Exception ${decision}`;
  let message = decision === 'approved'
    ? `Route deviation ${decision}: ${description}`
    : `Exception ${decision}: ${description}`;

  // Special handling for auto-approved exceptions
  if (decision === 'auto_approved' || autoApproved) {
    title = 'Exception Auto-Approved';
    message = `Smart system auto-approved: ${description}`;
  } else if (isAdaptive && decision === 'approved') {
    title = 'Smart Exception Approved';
    message = `AI-assisted exception approved: ${description}`;
  }

  broadcast({
    type: 'exception_updated',
    title,
    message,
    data: {
      ...exception,
      is_adaptive: isAdaptive,
      auto_approved: autoApproved
    },
    decision: autoApproved ? 'auto_approved' : decision,
    timestamp: new Date().toISOString()
  });
};

const notifyTripStatusChanged = (trip) => {
  // Notify relevant users about trip status changes
  broadcast({
    type: 'trip_status_changed',
    title: 'Trip Status Updated',
    message: `Trip ${trip.trip_number} status: ${trip.status}`,
    data: trip,
    timestamp: new Date().toISOString()
  });
};

// Route Discovery Notifications
const notifyRouteDiscoveryStarted = (assignment, location) => {
  // Notify when dynamic route discovery begins
  broadcast({
    type: 'route_discovery_started',
    title: 'Route Discovery Started',
    message: `Dynamic route discovery initiated for ${assignment.assignment_code} at ${location.name}`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      trigger_location: {
        id: location.id,
        name: location.name,
        type: location.type
      },
      discovery_mode: 'progressive'
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

const notifyRouteLocationConfirmed = (assignment, location, locationType) => {
  // Notify when a route location is confirmed through QR scan
  const isLoading = locationType === 'loading';
  const icon = isLoading ? '📍' : '🎯';
  const action = isLoading ? 'Loading location confirmed' : 'Unloading location confirmed';

  broadcast({
    type: 'route_location_confirmed',
    title: `${action}`,
    message: `${assignment.assignment_code}: ${location.name} confirmed via QR scan`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      confirmed_location: {
        id: location.id,
        name: location.name,
        type: location.type,
        role: locationType
      },
      discovery_progress: isLoading ? 'loading_confirmed' : 'unloading_confirmed'
    },
    icon,
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyRouteUpdated = (assignment, previousLocation, newLocation, locationType) => {
  // Notify when route is updated due to different location than predicted
  const isLoading = locationType === 'loading';
  const locationRole = isLoading ? 'loading' : 'unloading';

  broadcast({
    type: 'route_updated',
    title: 'Route Updated',
    message: `${assignment.assignment_code}: ${locationRole} location changed from ${previousLocation.name} to ${newLocation.name}`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      location_change: {
        type: locationType,
        previous: {
          id: previousLocation.id,
          name: previousLocation.name
        },
        new: {
          id: newLocation.id,
          name: newLocation.name
        }
      },
      discovery_type: 'route_correction'
    },
    timestamp: new Date().toISOString(),
    priority: 'high'
  });
};

const notifyRouteDiscoveryCompleted = (assignment, finalRoute) => {
  // Notify when route discovery is completed (both locations confirmed)
  broadcast({
    type: 'route_discovery_completed',
    title: 'Route Discovery Completed',
    message: `${assignment.assignment_code}: Complete route confirmed - ${finalRoute.loading_location} → ${finalRoute.unloading_location}`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      final_route: {
        loading_location: {
          id: finalRoute.loading_location_id,
          name: finalRoute.loading_location
        },
        unloading_location: {
          id: finalRoute.unloading_location_id,
          name: finalRoute.unloading_location
        }
      },
      discovery_status: 'completed'
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

// Multi-location workflow notifications
const notifyTripExtended = (trip, newLocation) => {
  broadcast({
    type: 'trip_extended',
    title: 'Trip Extended',
    message: `${trip.truck_number}: Trip extended to ${newLocation.name}`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      location_name: newLocation.name,
      workflow_type: 'extended',
      baseline_trip_id: trip.baseline_trip_id
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyCycleStarted = (trip, cycleNumber) => {
  broadcast({
    type: 'cycle_started',
    title: 'Cycle Trip Started',
    message: `${trip.truck_number}: Cycle #${cycleNumber} started`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      cycle_number: cycleNumber,
      workflow_type: 'cycle',
      baseline_trip_id: trip.baseline_trip_id
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyDynamicRouteDiscovered = (trip, newLocation) => {
  broadcast({
    type: 'dynamic_route',
    title: 'Dynamic Route Discovered',
    message: `${trip.truck_number}: New route discovered to ${newLocation.name}`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      location_name: newLocation.name,
      workflow_type: 'dynamic'
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyWorkflowCompleted = (trip, workflowType) => {
  broadcast({
    type: 'workflow_completed',
    title: 'Workflow Completed',
    message: `${trip.truck_number}: ${workflowType} workflow completed`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      workflow_type: workflowType,
      cycle_number: trip.cycle_number
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

// Get connection statistics
const getStats = () => {
  const totalClients = clients.size;
  const authenticatedClients = Array.from(clients.values()).filter(c => c.userId).length;
  const roles = {};

  clients.forEach(client => {
    if (client.role) {
      roles[client.role] = (roles[client.role] || 0) + 1;
    }
  });

  return {
    totalClients,
    authenticatedClients,
    roles,
    uptime: wss ? Date.now() - wss.startTime : 0
  };
};

module.exports = {
  initializeWebSocket,
  broadcast,
  sendToRole,
  sendToUser,
  notifyExceptionCreated,
  notifyExceptionUpdated,
  notifyTripStatusChanged,
  notifyRouteDiscoveryStarted,
  notifyRouteLocationConfirmed,
  notifyRouteUpdated,
  notifyRouteDiscoveryCompleted,
  notifyTripExtended,
  notifyCycleStarted,
  notifyDynamicRouteDiscovered,
  notifyWorkflowCompleted,
  getStats
};
