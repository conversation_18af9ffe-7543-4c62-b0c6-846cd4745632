const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function fixTrip2Status() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing Trip #2 Database Status...');

    // First, analyze current Trip #2 state
    const trip2Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time, tl.total_duration_minutes,
        tl.notes
      FROM trip_logs tl
      WHERE tl.id = 104
    `);

    if (trip2Result.rows.length === 0) {
      console.log('❌ Trip #2 (ID: 104) not found');
      return;
    }

    const trip2 = trip2Result.rows[0];
    console.log('\n📊 Current Trip #2 State:');
    console.log(`   Trip ID: ${trip2.id}`);
    console.log(`   Trip Number: ${trip2.trip_number}`);
    console.log(`   Current Status: ${trip2.status}`);
    console.log('');

    console.log('⏰ Timestamp Analysis:');
    console.log(`   Loading Start: ${trip2.loading_start_time || 'NULL'} ${trip2.loading_start_time ? '✅' : '❌'}`);
    console.log(`   Loading End: ${trip2.loading_end_time || 'NULL'} ${trip2.loading_end_time ? '✅' : '❌'}`);
    console.log(`   Unloading Start: ${trip2.unloading_start_time || 'NULL'} ${trip2.unloading_start_time ? '✅' : '❌'}`);
    console.log(`   Unloading End: ${trip2.unloading_end_time || 'NULL'} ${trip2.unloading_end_time ? '✅' : '❌'}`);
    console.log(`   Trip Completed: ${trip2.trip_completed_time || 'NULL'} ${trip2.trip_completed_time ? '✅' : '❌'}`);
    console.log('');

    // Determine the correct status based on actual progression
    let correctStatus = 'assigned';
    let statusReason = '';

    if (trip2.loading_start_time && trip2.loading_end_time && !trip2.unloading_start_time) {
      correctStatus = 'loading_end';
      statusReason = 'Trip completed loading phase but never started unloading';
    } else if (trip2.loading_start_time && !trip2.loading_end_time) {
      correctStatus = 'loading_start';
      statusReason = 'Trip started loading but never completed loading';
    } else if (trip2.unloading_start_time && trip2.unloading_end_time) {
      correctStatus = 'trip_completed';
      statusReason = 'Trip completed all phases naturally';
    } else if (trip2.unloading_start_time && !trip2.unloading_end_time) {
      correctStatus = 'unloading_start';
      statusReason = 'Trip started unloading but never completed unloading';
    }

    console.log('🎯 Status Analysis:');
    console.log(`   Current Database Status: ${trip2.status}`);
    console.log(`   Correct Status Should Be: ${correctStatus}`);
    console.log(`   Reason: ${statusReason}`);
    console.log('');

    if (trip2.status === correctStatus) {
      console.log('✅ Trip #2 status is already correct!');
      return;
    }

    console.log('🔄 Correcting Trip #2 Status...');

    await client.query('BEGIN');

    try {
      // Calculate correct duration based on actual progression
      let durationMinutes = null;
      if (correctStatus === 'loading_end' && trip2.loading_start_time && trip2.loading_end_time) {
        const loadingDuration = Math.round(
          (new Date(trip2.loading_end_time) - new Date(trip2.loading_start_time)) / (1000 * 60)
        );
        durationMinutes = loadingDuration;
      } else if (trip2.loading_start_time) {
        const totalDuration = Math.round(
          (new Date() - new Date(trip2.loading_start_time)) / (1000 * 60)
        );
        durationMinutes = totalDuration;
      }

      // Update the trip status to reflect actual progression
      const updateResult = await client.query(`
        UPDATE trip_logs 
        SET 
          status = $1,
          trip_completed_time = NULL,
          total_duration_minutes = $2,
          updated_at = CURRENT_TIMESTAMP,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb
        WHERE id = $4
        RETURNING *
      `, [
        correctStatus,
        durationMinutes,
        {
          status_correction: true,
          status_correction_reason: 'Corrected to reflect actual trip progression instead of forced completion',
          status_correction_timestamp: new Date().toISOString(),
          previous_status: trip2.status,
          corrected_status: correctStatus,
          correction_rationale: statusReason
        },
        trip2.id
      ]);

      await client.query('COMMIT');

      const updatedTrip = updateResult.rows[0];
      console.log('✅ Trip #2 status corrected successfully!');
      console.log(`   New Status: ${updatedTrip.status}`);
      console.log(`   Duration: ${updatedTrip.total_duration_minutes || 'NULL'} minutes`);
      console.log(`   Trip Completed Time: ${updatedTrip.trip_completed_time || 'NULL (removed)'}`);
      console.log('');

      console.log('🎯 Correction Summary:');
      console.log(`   ✅ Status changed from "${trip2.status}" to "${correctStatus}"`);
      console.log(`   ✅ Removed incorrect trip_completed_time`);
      console.log(`   ✅ Recalculated duration based on actual progression`);
      console.log(`   ✅ Added correction notes for audit trail`);
      console.log('');

      console.log('📋 Impact:');
      console.log('   ✅ Trip Monitoring Dashboard will now show correct status');
      console.log('   ✅ Database reflects actual trip progression');
      console.log('   ✅ Auto-completion logic preserved actual status');
      console.log('   ✅ Audit trail maintained with correction notes');

      // Verify the correction
      console.log('\n🔍 Verification:');
      const verifyResult = await client.query(`
        SELECT status, trip_completed_time, total_duration_minutes
        FROM trip_logs 
        WHERE id = $1
      `, [trip2.id]);

      const verifiedTrip = verifyResult.rows[0];
      console.log(`   Database Status: ${verifiedTrip.status}`);
      console.log(`   Trip Completed Time: ${verifiedTrip.trip_completed_time || 'NULL'}`);
      console.log(`   Duration: ${verifiedTrip.total_duration_minutes || 'NULL'} minutes`);

      if (verifiedTrip.status === correctStatus && !verifiedTrip.trip_completed_time) {
        console.log('   ✅ Verification PASSED - Status correction successful!');
      } else {
        console.log('   ❌ Verification FAILED - Status correction may have issues');
      }

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('❌ Error fixing Trip #2 status:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

fixTrip2Status().catch(console.error);
