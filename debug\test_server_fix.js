const http = require('http');

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testServerFix() {
  try {
    console.log('🧪 Testing Server Fix...');

    // Test the scanner endpoint with Point A QR code
    console.log('1. Testing scanner endpoint...');
    const scanData = {
      qr_code_data: "POINT_A_MAIN_LOADING_SITE",
      scanned_at: new Date().toISOString(),
      client_ip: "127.0.0.1"
    };

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/scanner/scan',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(JSON.stringify(scanData))
      }
    };

    try {
      const response = await makeRequest(options, scanData);

      if (response.status === 200) {
        console.log('   ✅ Scanner endpoint responded successfully');
        console.log('   Response:', response.data);
      } else {
        console.log('   ❌ Scanner endpoint failed');
        console.log('   Status:', response.status);
        console.log('   Error:', response.data);
      }

    } catch (error) {
      console.log('   ❌ Scanner endpoint failed');
      console.log('   Error:', error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testServerFix().catch(console.error);
