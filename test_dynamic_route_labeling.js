/**
 * Test script for Dynamic Route Labeling Fix
 * Validates that "🔄 Dynamic Route" indicators appear only during active discovery
 */

// Mock trip data for testing different scenarios
const mockTrips = {
  // Scenario 1: Pre-existing assignment (should NOT show dynamic route)
  establishedAssignment: {
    id: 1,
    trip_number: 1,
    status: 'loading_start',
    assignment_notes: '{}', // No dynamic assignment flag
    assignment_created_at: '2025-01-01T10:00:00Z', // Old assignment
    actual_loading_location_id: 10,
    actual_unloading_location_id: null,
    loading_location_id: 10,
    unloading_location_id: 15,
    loading_location_name: 'Loading Site A',
    unloading_location_name: 'Unloading Site B'
  },

  // Scenario 2: New auto-created assignment (should show dynamic route)
  newAutoAssignment: {
    id: 2,
    trip_number: 1,
    status: 'assigned',
    assignment_notes: '{"creation_method":"dynamic_assignment","auto_created":true}',
    assignment_created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    actual_loading_location_id: null,
    actual_unloading_location_id: null,
    loading_location_id: null,
    unloading_location_id: null,
    loading_location_name: 'Loading Site A',
    unloading_location_name: 'Unloading Site B'
  },

  // Scenario 3: Old auto-created assignment (should NOT show dynamic route)
  oldAutoAssignment: {
    id: 3,
    trip_number: 2,
    status: 'trip_completed',
    assignment_notes: '{"creation_method":"dynamic_assignment","auto_created":true}',
    assignment_created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    actual_loading_location_id: 10,
    actual_unloading_location_id: 15,
    loading_location_id: 10,
    unloading_location_id: 15,
    loading_location_name: 'Loading Site A',
    unloading_location_name: 'Unloading Site B'
  },

  // Scenario 4: Auto-created with uncertain locations (should show dynamic route)
  uncertainLocations: {
    id: 4,
    trip_number: 1,
    status: 'assigned',
    assignment_notes: '{"creation_method":"dynamic_assignment","auto_created":true}',
    assignment_created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
    actual_loading_location_id: null,
    actual_unloading_location_id: null,
    loading_location_id: null,
    unloading_location_id: null,
    loading_location_name: 'Loading Site A',
    unloading_location_name: 'Unloading Site B'
  },

  // Scenario 5: Auto-created with confirmed locations (should NOT show dynamic route)
  confirmedLocations: {
    id: 5,
    trip_number: 1,
    status: 'trip_completed',
    assignment_notes: '{"creation_method":"dynamic_assignment","auto_created":true}',
    assignment_created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
    actual_loading_location_id: 10,
    actual_unloading_location_id: 15,
    loading_location_id: 10,
    unloading_location_id: 15,
    loading_location_name: 'Loading Site A',
    unloading_location_name: 'Unloading Site B'
  }
};

// Mock the dynamic route detection logic from TripsTable.js
function testDynamicRouteDetection(trip) {
  console.log(`\n🧪 Testing Trip ID ${trip.id}:`);
  console.log(`   Status: ${trip.status}`);
  console.log(`   Assignment Notes: ${trip.assignment_notes}`);
  console.log(`   Assignment Age: ${Math.round((new Date() - new Date(trip.assignment_created_at)) / (60 * 1000))} minutes`);
  console.log(`   Actual Loading Location: ${trip.actual_loading_location_id || 'null'}`);
  console.log(`   Actual Unloading Location: ${trip.actual_unloading_location_id || 'null'}`);

  // Check if this is a dynamic assignment
  const isDynamicAssignment = trip.assignment_notes &&
    JSON.parse(trip.assignment_notes || '{}').creation_method === 'dynamic_assignment';

  console.log(`   Is Dynamic Assignment: ${isDynamicAssignment}`);

  if (!isDynamicAssignment) {
    console.log(`   ✅ RESULT: Normal route display (no dynamic indicator)`);
    return { showDynamicRoute: false, reason: 'Not a dynamic assignment' };
  }

  // CRITICAL FIX: Determine if dynamic route discovery is actively in progress
  const assignmentAge = new Date() - new Date(trip.assignment_created_at);
  const isRecentlyCreated = assignmentAge < (30 * 60 * 1000); // 30 minutes
  
  // CORRECTED LOGIC: Check if assignment exists in Assignment Management with established locations
  const hasEstablishedLoadingLocation = trip.loading_location_id !== null && trip.loading_location_id !== undefined;
  const hasEstablishedUnloadingLocation = trip.unloading_location_id !== null && trip.unloading_location_id !== undefined;
  const existsInAssignmentManagement = hasEstablishedLoadingLocation && hasEstablishedUnloadingLocation;

  // If assignment exists in Assignment Management, NEVER show Dynamic Route
  const isActiveDiscovery = existsInAssignmentManagement ? false : true;

  console.log(`   Assignment Loading Location ID: ${trip.loading_location_id}`);
  console.log(`   Assignment Unloading Location ID: ${trip.unloading_location_id}`);
  console.log(`   Has Established Loading Location: ${hasEstablishedLoadingLocation}`);
  console.log(`   Has Established Unloading Location: ${hasEstablishedUnloadingLocation}`);
  console.log(`   Exists in Assignment Management: ${existsInAssignmentManagement}`);
  console.log(`   Is Active Discovery: ${isActiveDiscovery}`);

  if (isActiveDiscovery) {
    console.log(`   ✅ RESULT: Show "🔄 Dynamic Route" indicator`);
    return { 
      showDynamicRoute: true, 
      reason: isRecentlyCreated ? 'Recently created assignment' : 'Uncertain locations' 
    };
  } else {
    console.log(`   ✅ RESULT: Normal route display (discovery complete)`);
    return { showDynamicRoute: false, reason: 'Discovery phase complete' };
  }
}

// Test all scenarios
function runDynamicRouteLabelingTests() {
  console.log('🧪 Dynamic Route Labeling Fix - Test Suite\n');
  console.log('=' .repeat(60));

  const testResults = [];

  // Test Scenario 1: Established Assignment
  console.log('\n📋 SCENARIO 1: Pre-existing Assignment (DT-100 with established assignment)');
  console.log('Expected: Should show "Loading Site A → Unloading Site B" (NO dynamic route label)');
  const result1 = testDynamicRouteDetection(mockTrips.establishedAssignment);
  testResults.push({
    scenario: 'Established Assignment',
    expected: false,
    actual: result1.showDynamicRoute,
    passed: result1.showDynamicRoute === false
  });

  // Test Scenario 2: New Auto Assignment
  console.log('\n📋 SCENARIO 2: New Auto-Created Assignment (First scan, <30 minutes)');
  console.log('Expected: Should show "🔄 Dynamic Route" initially');
  const result2 = testDynamicRouteDetection(mockTrips.newAutoAssignment);
  testResults.push({
    scenario: 'New Auto Assignment',
    expected: true,
    actual: result2.showDynamicRoute,
    passed: result2.showDynamicRoute === true
  });

  // Test Scenario 3: Old Auto Assignment
  console.log('\n📋 SCENARIO 3: Old Auto-Created Assignment (>30 minutes, established)');
  console.log('Expected: Should show normal route (NO dynamic route label)');
  const result3 = testDynamicRouteDetection(mockTrips.oldAutoAssignment);
  testResults.push({
    scenario: 'Old Auto Assignment',
    expected: false,
    actual: result3.showDynamicRoute,
    passed: result3.showDynamicRoute === false
  });

  // Test Scenario 4: Uncertain Locations
  console.log('\n📋 SCENARIO 4: Auto-Created with Uncertain Locations (>30 min but uncertain)');
  console.log('Expected: Should show "🔄 Dynamic Route" due to uncertain locations');
  const result4 = testDynamicRouteDetection(mockTrips.uncertainLocations);
  testResults.push({
    scenario: 'Uncertain Locations',
    expected: true,
    actual: result4.showDynamicRoute,
    passed: result4.showDynamicRoute === true
  });

  // Test Scenario 5: Confirmed Locations
  console.log('\n📋 SCENARIO 5: Auto-Created with Confirmed Locations (discovery complete)');
  console.log('Expected: Should show normal route (NO dynamic route label)');
  const result5 = testDynamicRouteDetection(mockTrips.confirmedLocations);
  testResults.push({
    scenario: 'Confirmed Locations',
    expected: false,
    actual: result5.showDynamicRoute,
    passed: result5.showDynamicRoute === false
  });

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 TEST RESULTS SUMMARY');
  console.log('=' .repeat(60));

  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;

  testResults.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.scenario}: Expected ${result.expected}, Got ${result.actual}`);
  });

  console.log(`\n📊 Overall: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 SUCCESS: All dynamic route labeling tests passed!');
    console.log('✅ "🔄 Dynamic Route" appears only during active discovery');
    console.log('✅ Established assignments show normal route format');
    console.log('✅ No regression in legitimate dynamic route discovery');
  } else {
    console.log('❌ FAILURE: Some tests failed - fix needs adjustment');
  }

  return { passed: passedTests, total: totalTests, allPassed: passedTests === totalTests };
}

// Run the tests
if (require.main === module) {
  runDynamicRouteLabelingTests();
}

module.exports = { 
  runDynamicRouteLabelingTests,
  testDynamicRouteDetection,
  mockTrips
};
