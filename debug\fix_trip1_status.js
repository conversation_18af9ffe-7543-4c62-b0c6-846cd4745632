const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function fixTrip1Status() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing Trip #1 Database Status...');

    // First, get current Trip #1 state
    const trip1Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time, tl.total_duration_minutes,
        tl.unloading_duration_minutes,
        tl.notes
      FROM trip_logs tl
      WHERE tl.id = 105
    `);

    if (trip1Result.rows.length === 0) {
      console.log('❌ Trip #1 (ID: 105) not found');
      return;
    }

    const trip1 = trip1Result.rows[0];
    console.log('\n📊 Current Trip #1 State:');
    console.log(`   Trip ID: ${trip1.id}`);
    console.log(`   Trip Number: ${trip1.trip_number}`);
    console.log(`   Current Status: ${trip1.status}`);
    console.log('');

    console.log('⏰ Timestamp Analysis:');
    console.log(`   Loading Start: ${trip1.loading_start_time || 'NULL'} ${trip1.loading_start_time ? '✅' : '❌'}`);
    console.log(`   Loading End: ${trip1.loading_end_time || 'NULL'} ${trip1.loading_end_time ? '✅' : '❌'}`);
    console.log(`   Unloading Start: ${trip1.unloading_start_time || 'NULL'} ${trip1.unloading_start_time ? '✅' : '❌'}`);
    console.log(`   Unloading End: ${trip1.unloading_end_time || 'NULL'} ${trip1.unloading_end_time ? '✅' : '❌'}`);
    console.log(`   Trip Completed: ${trip1.trip_completed_time || 'NULL'} ${trip1.trip_completed_time ? '✅' : '❌'}`);
    console.log('');

    // Determine the correct status based on actual progression
    const hasLoadingPhase = !!(trip1.loading_start_time && trip1.loading_end_time);
    const hasUnloadingPhase = !!(trip1.unloading_start_time && trip1.unloading_end_time);

    let correctStatus = 'assigned';
    let statusReason = '';

    if (hasUnloadingPhase && !hasLoadingPhase) {
      correctStatus = 'unloading_end';
      statusReason = 'Trip completed unloading phase but never had loading phase (dynamic route discovery)';
    } else if (hasLoadingPhase && hasUnloadingPhase) {
      correctStatus = 'trip_completed';
      statusReason = 'Trip completed all phases naturally';
    } else if (hasLoadingPhase && !hasUnloadingPhase) {
      correctStatus = 'loading_end';
      statusReason = 'Trip completed loading phase but never started unloading';
    } else if (trip1.unloading_start_time && !trip1.unloading_end_time) {
      correctStatus = 'unloading_start';
      statusReason = 'Trip started unloading but never completed unloading';
    } else if (trip1.loading_start_time && !trip1.loading_end_time) {
      correctStatus = 'loading_start';
      statusReason = 'Trip started loading but never completed loading';
    }

    console.log('🎯 Status Analysis:');
    console.log(`   Current Database Status: ${trip1.status}`);
    console.log(`   Correct Status Should Be: ${correctStatus}`);
    console.log(`   Reason: ${statusReason}`);
    console.log('');

    if (trip1.status === correctStatus) {
      console.log('✅ Trip #1 status is already correct!');
      return;
    }

    console.log('🔄 Correcting Trip #1 Status...');

    await client.query('BEGIN');

    try {
      // Calculate correct duration based on actual progression
      let durationMinutes = null;
      if (correctStatus === 'unloading_end' && trip1.unloading_start_time && trip1.unloading_end_time) {
        const unloadingDuration = Math.round(
          (new Date(trip1.unloading_end_time) - new Date(trip1.unloading_start_time)) / (1000 * 60)
        );
        durationMinutes = unloadingDuration;
        console.log(`   Calculated unloading duration: ${unloadingDuration} minutes`);
      } else if (correctStatus === 'loading_end' && trip1.loading_start_time && trip1.loading_end_time) {
        const loadingDuration = Math.round(
          (new Date(trip1.loading_end_time) - new Date(trip1.loading_start_time)) / (1000 * 60)
        );
        durationMinutes = loadingDuration;
        console.log(`   Calculated loading duration: ${loadingDuration} minutes`);
      }

      // Update the trip status to reflect actual progression
      const updateResult = await client.query(`
        UPDATE trip_logs 
        SET 
          status = $1,
          trip_completed_time = NULL,
          total_duration_minutes = $2,
          unloading_duration_minutes = $3,
          updated_at = CURRENT_TIMESTAMP,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $4::jsonb
        WHERE id = $5
        RETURNING *
      `, [
        correctStatus,
        durationMinutes,
        correctStatus === 'unloading_end' ? durationMinutes : null,
        {
          status_correction: true,
          status_correction_reason: 'Corrected to reflect actual trip progression instead of forced completion',
          status_correction_timestamp: new Date().toISOString(),
          previous_status: trip1.status,
          corrected_status: correctStatus,
          correction_rationale: statusReason,
          correction_type: 'unloading_phase_only'
        },
        trip1.id
      ]);

      await client.query('COMMIT');

      const updatedTrip = updateResult.rows[0];
      console.log('✅ Trip #1 status corrected successfully!');
      console.log(`   New Status: ${updatedTrip.status}`);
      console.log(`   Total Duration: ${updatedTrip.total_duration_minutes || 'NULL'} minutes`);
      console.log(`   Unloading Duration: ${updatedTrip.unloading_duration_minutes || 'NULL'} minutes`);
      console.log(`   Trip Completed Time: ${updatedTrip.trip_completed_time || 'NULL (removed)'}`);
      console.log('');

      console.log('🎯 Correction Summary:');
      console.log(`   ✅ Status changed from "${trip1.status}" to "${correctStatus}"`);
      console.log(`   ✅ Removed incorrect trip_completed_time`);
      console.log(`   ✅ Set duration to unloading phase only (${durationMinutes} minutes)`);
      console.log(`   ✅ Added correction notes for audit trail`);
      console.log('');

      console.log('📋 Impact:');
      console.log('   ✅ Trip Monitoring Dashboard will now show "Unloading Complete" status');
      console.log('   ✅ Database reflects actual trip progression (unloading-only)');
      console.log('   ✅ Consistent with dynamic route discovery pattern');
      console.log('   ✅ Audit trail maintained with correction notes');

      // Verify the correction
      console.log('\n🔍 Verification:');
      const verifyResult = await client.query(`
        SELECT status, trip_completed_time, total_duration_minutes, unloading_duration_minutes
        FROM trip_logs 
        WHERE id = $1
      `, [trip1.id]);

      const verifiedTrip = verifyResult.rows[0];
      console.log(`   Database Status: ${verifiedTrip.status}`);
      console.log(`   Trip Completed Time: ${verifiedTrip.trip_completed_time || 'NULL'}`);
      console.log(`   Total Duration: ${verifiedTrip.total_duration_minutes || 'NULL'} minutes`);
      console.log(`   Unloading Duration: ${verifiedTrip.unloading_duration_minutes || 'NULL'} minutes`);

      const isCorrect = verifiedTrip.status === correctStatus && !verifiedTrip.trip_completed_time;
      console.log(`   ✅ Verification: ${isCorrect ? 'PASSED - Status correction successful!' : 'FAILED - Status correction may have issues'}`);

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('❌ Error fixing Trip #1 status:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

fixTrip1Status().catch(console.error);
