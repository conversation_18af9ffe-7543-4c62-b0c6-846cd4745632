const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function populateLocationSequence() {
  const client = await pool.connect();
  try {
    console.log('🔧 Populating location_sequence for existing trips...\n');
    
    // First, check how many trips need updating
    const countResult = await client.query(`
      SELECT COUNT(*) as count
      FROM trip_logs 
      WHERE location_sequence IS NULL 
        AND status = 'trip_completed'
    `);
    
    console.log(`📊 Found ${countResult.rows[0].count} completed trips without location_sequence`);
    
    if (countResult.rows[0].count === '0') {
      console.log('✅ All trips already have location sequences');
      return;
    }
    
    // Update trips with location sequences
    const result = await client.query(`
      UPDATE trip_logs 
      SET location_sequence = jsonb_build_array(
        jsonb_build_object(
          'name', COALESCE(ll.name, 'Unknown Loading'),
          'type', 'loading',
          'confirmed', trip_logs.loading_start_time IS NOT NULL,
          'location_id', COALESCE(trip_logs.actual_loading_location_id, a.loading_location_id)
        ),
        jsonb_build_object(
          'name', COALESCE(ul.name, 'Unknown Unloading'),
          'type', 'unloading', 
          'confirmed', trip_logs.unloading_start_time IS NOT NULL,
          'location_id', COALESCE(trip_logs.actual_unloading_location_id, a.unloading_location_id)
        )
      ),
      workflow_type = 'standard',
      updated_at = CURRENT_TIMESTAMP
      FROM assignments a
      LEFT JOIN locations ll ON COALESCE(trip_logs.actual_loading_location_id, a.loading_location_id) = ll.id
      LEFT JOIN locations ul ON COALESCE(trip_logs.actual_unloading_location_id, a.unloading_location_id) = ul.id
      WHERE trip_logs.assignment_id = a.id 
        AND trip_logs.location_sequence IS NULL
        AND trip_logs.status = 'trip_completed'
    `);
    
    console.log(`✅ Updated ${result.rowCount} trips with location sequences`);
    
    // Verify the update
    const verifyResult = await client.query(`
      SELECT id, status, workflow_type, 
             jsonb_array_length(location_sequence) as sequence_length
      FROM trip_logs 
      WHERE location_sequence IS NOT NULL
      ORDER BY updated_at DESC
      LIMIT 3
    `);
    
    console.log('\n📋 Sample updated trips:');
    verifyResult.rows.forEach(row => {
      console.log(`  Trip ${row.id}: ${row.status} | ${row.workflow_type} | ${row.sequence_length} locations`);
    });
    
  } finally {
    client.release();
    await pool.end();
  }
}

async function createTestExtendedTrip() {
  const client = await pool.connect();
  try {
    console.log('\n🧪 Creating test extended trip...');
    
    // Get a completed trip to extend
    const baseTrip = await client.query(`
      SELECT tl.*, a.truck_id, a.driver_id, a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.status = 'trip_completed'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `);
    
    if (baseTrip.rows.length === 0) {
      console.log('❌ No completed trips found to extend');
      return;
    }
    
    const baseTripData = baseTrip.rows[0];
    console.log(`📋 Using base trip ${baseTripData.id} for extension`);
    
    // Get a different loading location
    const newLocation = await client.query(`
      SELECT * FROM locations 
      WHERE type = 'loading' 
        AND id != $1 
      LIMIT 1
    `, [baseTripData.loading_location_id]);
    
    if (newLocation.rows.length === 0) {
      console.log('❌ No alternative loading location found');
      return;
    }
    
    const extensionLocation = newLocation.rows[0];
    console.log(`📍 Using ${extensionLocation.name} as extension location`);
    
    // Create assignment for extended trip
    const assignment = await client.query(`
      INSERT INTO assignments (
        truck_id, driver_id, loading_location_id, unloading_location_id,
        assigned_date, created_at, updated_at, notes
      )
      VALUES ($1, $2, $3, $4, CURRENT_DATE + INTERVAL '1 day', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $5)
      ON CONFLICT DO NOTHING
      RETURNING *
    `, [
      baseTripData.truck_id,
      baseTripData.driver_id,
      extensionLocation.id,
      baseTripData.unloading_location_id,
      JSON.stringify({ creation_method: 'test_extended_trip', baseline_trip_id: baseTripData.id })
    ]);
    
    let assignmentId;
    if (assignment.rows.length > 0) {
      assignmentId = assignment.rows[0].id;
      console.log(`✅ Created assignment ${assignmentId}`);
    } else {
      // Get existing assignment
      const existing = await client.query(`
        SELECT id FROM assignments 
        WHERE truck_id = $1 AND driver_id = $2 
          AND loading_location_id = $3 AND unloading_location_id = $4
        LIMIT 1
      `, [
        baseTripData.truck_id,
        baseTripData.driver_id,
        extensionLocation.id,
        baseTripData.unloading_location_id
      ]);
      assignmentId = existing.rows[0].id;
      console.log(`📋 Using existing assignment ${assignmentId}`);
    }
    
    // Get next trip number
    const nextTrip = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs WHERE assignment_id = $1
    `, [assignmentId]);
    
    const tripNumber = nextTrip.rows[0].next_trip_number;
    
    // Create extended trip
    const extendedTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time,
        trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
        location_sequence,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING *
    `, [
      assignmentId,
      tripNumber,
      'trip_completed',
      new Date(Date.now() - 3600000), // 1 hour ago
      new Date(Date.now() - 3300000), // 55 minutes ago
      new Date(Date.now() - 1800000), // 30 minutes ago
      new Date(Date.now() - 1500000), // 25 minutes ago
      new Date(Date.now() - 1200000), // 20 minutes ago
      extensionLocation.id,
      baseTripData.unloading_location_id,
      true, // is_extended_trip
      'extended', // workflow_type
      baseTripData.id, // baseline_trip_id
      1, // cycle_number
      JSON.stringify([
        {
          name: extensionLocation.name,
          type: 'loading',
          confirmed: true,
          location_id: extensionLocation.id
        },
        {
          name: 'Point B - Primary Dump Site',
          type: 'unloading',
          confirmed: true,
          location_id: baseTripData.unloading_location_id
        }
      ]),
      new Date(),
      new Date()
    ]);
    
    console.log(`✅ Created extended trip ${extendedTrip.rows[0].id}`);
    
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await populateLocationSequence();
    await createTestExtendedTrip();
    console.log('\n🎉 Multi-location workflow data setup complete!');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main();
