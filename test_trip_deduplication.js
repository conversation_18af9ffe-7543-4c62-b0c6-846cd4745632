/**
 * Test script to validate trip deduplication fix
 * This script simulates the scenario where Auto Create Assignment
 * would previously create duplicate trips
 */

const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

async function testTripDeduplication() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Testing Trip Deduplication Fix...\n');

    // Step 1: Create test data
    console.log('📋 Setting up test data...');
    
    // Create test truck
    const truckResult = await client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, qr_code_data, status)
      VALUES ('TEST-100', 'TEST-100-LP', '{"type":"truck","id":"TEST-100"}', 'active')
      ON CONFLICT (truck_number) DO UPDATE SET status = 'active'
      RETURNING id, truck_number
    `);
    const truck = truckResult.rows[0];
    console.log(`✅ Test truck created: ${truck.truck_number} (ID: ${truck.id})`);

    // Create test driver
    const driverResult = await client.query(`
      INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, hire_date, status)
      VALUES ('TEST-DRIVER', 'Test Driver', 'TEST-LIC-123', '2025-12-31', '2024-01-01', 'active')
      ON CONFLICT (employee_id) DO UPDATE SET status = 'active'
      RETURNING id, full_name
    `);
    const driver = driverResult.rows[0];
    console.log(`✅ Test driver created: ${driver.full_name} (ID: ${driver.id})`);

    // Create test locations
    const loadingLocationResult = await client.query(`
      INSERT INTO locations (location_code, name, type, qr_code_data, status)
      VALUES ('TEST-LOAD', 'Test Loading Site', 'loading', '{"type":"location","id":"TEST-LOAD"}', 'active')
      ON CONFLICT (location_code) DO UPDATE SET status = 'active'
      RETURNING id, name
    `);
    const loadingLocation = loadingLocationResult.rows[0];

    const unloadingLocationResult = await client.query(`
      INSERT INTO locations (location_code, name, type, qr_code_data, status)
      VALUES ('TEST-UNLOAD', 'Test Unloading Site', 'unloading', '{"type":"location","id":"TEST-UNLOAD"}', 'active')
      ON CONFLICT (location_code) DO UPDATE SET status = 'active'
      RETURNING id, name
    `);
    const unloadingLocation = unloadingLocationResult.rows[0];
    console.log(`✅ Test locations created: ${loadingLocation.name}, ${unloadingLocation.name}`);

    // Step 2: Create initial assignment and trip
    console.log('\n📋 Creating initial assignment and trip...');
    
    const assignment1Result = await client.query(`
      INSERT INTO assignments (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, status, assigned_date)
      VALUES ('TEST-ASSIGN-1', $1, $2, $3, $4, 'assigned', CURRENT_DATE)
      RETURNING id, assignment_code
    `, [truck.id, driver.id, loadingLocation.id, unloadingLocation.id]);
    const assignment1 = assignment1Result.rows[0];
    console.log(`✅ Initial assignment created: ${assignment1.assignment_code} (ID: ${assignment1.id})`);

    const trip1Result = await client.query(`
      INSERT INTO trip_logs (assignment_id, trip_number, status, loading_start_time, actual_loading_location_id)
      VALUES ($1, 1, 'loading_start', CURRENT_TIMESTAMP, $2)
      RETURNING id, trip_number, status
    `, [assignment1.id, loadingLocation.id]);
    const trip1 = trip1Result.rows[0];
    console.log(`✅ Initial trip created: Trip #${trip1.trip_number} (ID: ${trip1.id}, Status: ${trip1.status})`);

    // Step 3: Simulate Auto Create Assignment scenario
    console.log('\n🔄 Simulating Auto Create Assignment scenario...');
    
    // Create new assignment (simulating auto-assignment creation)
    const assignment2Result = await client.query(`
      INSERT INTO assignments (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, status, assigned_date, notes)
      VALUES ('TEST-AUTO-ASSIGN', $1, $2, $3, $4, 'assigned', CURRENT_DATE, '{"creation_method":"dynamic_assignment","auto_created":true}')
      RETURNING id, assignment_code
    `, [truck.id, driver.id, loadingLocation.id, unloadingLocation.id]);
    const assignment2 = assignment2Result.rows[0];
    console.log(`✅ Auto assignment created: ${assignment2.assignment_code} (ID: ${assignment2.id})`);

    // Step 4: Test the deduplication logic
    console.log('\n🧪 Testing trip deduplication logic...');
    
    // Check for existing active trips by truck_id (this is our fix)
    const existingTripResult = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        a.assignment_code, a.truck_id,
        dt.truck_number
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.id = $1
        AND tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
      LIMIT 1
    `, [truck.id]);

    if (existingTripResult.rows.length > 0) {
      const existingTrip = existingTripResult.rows[0];
      console.log(`✅ DEDUPLICATION WORKING: Found existing active trip for truck ${truck.truck_number}`);
      console.log(`   - Trip ID: ${existingTrip.id}`);
      console.log(`   - Current Assignment: ${existingTrip.assignment_code}`);
      console.log(`   - Status: ${existingTrip.status}`);
      
      // Simulate updating the trip instead of creating a new one
      const updateResult = await client.query(`
        UPDATE trip_logs 
        SET assignment_id = $1,
            updated_at = CURRENT_TIMESTAMP,
            notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb
        WHERE id = $3 
        RETURNING id, assignment_id, status
      `, [
        assignment2.id,
        {
          assignment_updated: true,
          old_assignment_id: assignment1.id,
          new_assignment_id: assignment2.id,
          update_reason: 'Auto Create Assignment triggered location update',
          test_scenario: true
        },
        existingTrip.id
      ]);
      
      const updatedTrip = updateResult.rows[0];
      console.log(`✅ TRIP UPDATED: Trip ${existingTrip.id} now uses assignment ${assignment2.id}`);
      
      // Verify no duplicate trips exist
      const tripCountResult = await client.query(`
        SELECT COUNT(*) as trip_count
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = $1
          AND tl.status NOT IN ('trip_completed', 'cancelled')
      `, [truck.id]);
      
      const tripCount = parseInt(tripCountResult.rows[0].trip_count);
      console.log(`✅ VERIFICATION: ${tripCount} active trip(s) for truck ${truck.truck_number}`);
      
      if (tripCount === 1) {
        console.log('🎉 SUCCESS: No duplicate trips created!');
      } else {
        console.log('❌ FAILURE: Multiple active trips detected!');
      }
    } else {
      console.log('❌ ERROR: No existing trip found for deduplication test');
    }

    // Step 5: Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    await client.query('DELETE FROM trip_logs WHERE assignment_id IN ($1, $2)', [assignment1.id, assignment2.id]);
    await client.query('DELETE FROM assignments WHERE id IN ($1, $2)', [assignment1.id, assignment2.id]);
    await client.query('DELETE FROM dump_trucks WHERE truck_number = $1', [truck.truck_number]);
    await client.query('DELETE FROM drivers WHERE employee_id = $1', ['TEST-DRIVER']);
    await client.query('DELETE FROM locations WHERE location_code IN ($1, $2)', ['TEST-LOAD', 'TEST-UNLOAD']);
    console.log('✅ Test data cleaned up');

    console.log('\n🎯 Trip Deduplication Test Complete!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the test
if (require.main === module) {
  testTripDeduplication().catch(console.error);
}

module.exports = { testTripDeduplication };
