const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function testPostCompletion() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Testing Post-Completion Detection\n');
    
    // Get DT-100 truck ID
    const truck = await client.query(`
      SELECT id, truck_number FROM dump_trucks WHERE truck_number = 'DT-100'
    `);
    
    if (truck.rows.length === 0) {
      console.log('❌ DT-100 not found');
      return;
    }
    
    const truckData = truck.rows[0];
    console.log(`📋 Testing with truck: ${truckData.truck_number} (ID: ${truckData.id})`);
    
    // Get Point C location
    const location = await client.query(`
      SELECT id, name, type, location_code FROM locations WHERE name = 'POINT C - LOADING'
    `);
    
    if (location.rows.length === 0) {
      console.log('❌ POINT C - LOADING not found');
      return;
    }
    
    const locationC = location.rows[0];
    console.log(`📍 Testing with location: ${locationC.name} (ID: ${locationC.id})`);
    
    // Test checkRecentCompletedTrip function manually
    console.log('\n🔍 Testing checkRecentCompletedTrip...');
    
    const recentTrip = await client.query(`
      SELECT
        tl.*,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location_name,
        ul.name as unloading_location_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.status = 'trip_completed'
        AND tl.trip_completed_time > (CURRENT_TIMESTAMP - INTERVAL '30 minutes')
      ORDER BY tl.trip_completed_time DESC
      LIMIT 1
    `, [truckData.id]);
    
    if (recentTrip.rows.length > 0) {
      const trip = recentTrip.rows[0];
      console.log(`  ✅ Found recent completed trip: ${trip.id}`);
      console.log(`    Status: ${trip.status}`);
      console.log(`    Route: ${trip.loading_location_name} → ${trip.unloading_location_name}`);
      console.log(`    Completed: ${trip.trip_completed_time}`);
      console.log(`    Actual Loading ID: ${trip.actual_loading_location_id}`);
      console.log(`    Actual Unloading ID: ${trip.actual_unloading_location_id}`);
      
      // Test workflow type determination using enhanced logic
      console.log('\n🔍 Testing workflow type determination...');

      // Use actual location IDs if available, fallback to assignment location IDs
      const actualLoadingId = trip.actual_loading_location_id || trip.loading_location_id;
      const actualUnloadingId = trip.actual_unloading_location_id || trip.unloading_location_id;

      let workflowType = 'none';
      if (locationC.id !== actualLoadingId && locationC.id !== actualUnloadingId) {
        workflowType = 'extended'; // A→B→C extension
      } else if (locationC.id === actualUnloadingId) {
        workflowType = 'cycle'; // C→B→C cycle
      }

      console.log(`    Location C ID: ${locationC.id}`);
      console.log(`    Trip Actual Loading ID: ${actualLoadingId}`);
      console.log(`    Trip Actual Unloading ID: ${actualUnloadingId}`);
      console.log(`    Workflow Type: ${workflowType}`);
      
      if (workflowType === 'extended') {
        console.log('\n✅ POST-COMPLETION DETECTION SHOULD WORK!');
        console.log('  The system should create an extended workflow for C→B');
        
        // Check why it's not working - look for active trips
        console.log('\n🔍 Checking for active trips that might interfere...');
        
        const activeTrips = await client.query(`
          SELECT 
            tl.id, tl.status, tl.trip_number,
            a.assignment_code,
            ll.name as loading_name, ul.name as unloading_name
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          JOIN dump_trucks dt ON a.truck_id = dt.id
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          WHERE dt.id = $1
            AND tl.status NOT IN ('trip_completed', 'cancelled')
          ORDER BY tl.created_at DESC
        `, [truckData.id]);
        
        if (activeTrips.rows.length > 0) {
          console.log('  ❌ FOUND ACTIVE TRIPS - This is the problem!');
          activeTrips.rows.forEach(trip => {
            console.log(`    Trip ${trip.id}: ${trip.status} | ${trip.loading_name} → ${trip.unloading_name}`);
          });
          console.log('\n  The post-completion detection only runs when there are NO active trips.');
          console.log('  But Trip 174 is still active (unloading_end), so the detection is skipped.');
          console.log('  This is why the system falls back to dynamic assignment creation.');
        } else {
          console.log('  ✅ No active trips found - post-completion should work');
        }
        
      } else {
        console.log(`\n❌ Workflow type is ${workflowType}, not extended`);
      }
      
    } else {
      console.log('  ❌ No recent completed trips found');
      console.log('  This means checkRecentCompletedTrip would return null');
    }
    
    console.log('\n🎉 Post-Completion Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testPostCompletion().catch(console.error);
