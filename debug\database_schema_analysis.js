const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeDatabaseSchemaAndDataPopulation() {
  const client = await pool.connect();
  try {
    console.log('🔍 DATABASE SCHEMA AND DATA POPULATION INVESTIGATION');
    console.log('=' .repeat(80));

    // 1. Analyze trip_logs table schema and NULL value patterns
    console.log('\n📋 TRIP_LOGS TABLE SCHEMA ANALYSIS');
    console.log('-' .repeat(50));
    
    const tripLogsSchema = await client.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('Trip Logs Table Structure:');
    tripLogsSchema.rows.forEach(col => {
      const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
      const defaultVal = col.column_default || 'No default';
      console.log(`  ${col.column_name}: ${col.data_type} ${nullable} (Default: ${defaultVal})`);
    });

    // 2. Analyze assignments table schema
    console.log('\n📋 ASSIGNMENTS TABLE SCHEMA ANALYSIS');
    console.log('-' .repeat(50));
    
    const assignmentsSchema = await client.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'assignments' 
        AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('Assignments Table Structure:');
    assignmentsSchema.rows.forEach(col => {
      const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
      const defaultVal = col.column_default || 'No default';
      console.log(`  ${col.column_name}: ${col.data_type} ${nullable} (Default: ${defaultVal})`);
    });

    // 3. Analyze NULL value patterns in trip_logs
    console.log('\n🔍 NULL VALUE PATTERNS IN TRIP_LOGS');
    console.log('-' .repeat(50));
    
    const nullAnalysis = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(loading_start_time) as has_loading_start,
        COUNT(loading_end_time) as has_loading_end,
        COUNT(unloading_start_time) as has_unloading_start,
        COUNT(unloading_end_time) as has_unloading_end,
        COUNT(trip_completed_time) as has_trip_completed,
        COUNT(actual_loading_location_id) as has_actual_loading,
        COUNT(actual_unloading_location_id) as has_actual_unloading,
        COUNT(CASE WHEN actual_loading_location_id IS NULL THEN 1 END) as null_actual_loading,
        COUNT(CASE WHEN actual_unloading_location_id IS NULL THEN 1 END) as null_actual_unloading
      FROM trip_logs
      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
    `);
    
    const nullStats = nullAnalysis.rows[0];
    console.log('NULL Value Statistics (Last 7 days):');
    console.log(`  Total Trips: ${nullStats.total_trips}`);
    console.log(`  Has Loading Start: ${nullStats.has_loading_start} (${((nullStats.has_loading_start/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  Has Loading End: ${nullStats.has_loading_end} (${((nullStats.has_loading_end/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  Has Unloading Start: ${nullStats.has_unloading_start} (${((nullStats.has_unloading_start/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  Has Unloading End: ${nullStats.has_unloading_end} (${((nullStats.has_unloading_end/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  Has Trip Completed: ${nullStats.has_trip_completed} (${((nullStats.has_trip_completed/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  Has Actual Loading Location: ${nullStats.has_actual_loading} (${((nullStats.has_actual_loading/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  Has Actual Unloading Location: ${nullStats.has_actual_unloading} (${((nullStats.has_actual_unloading/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  NULL Actual Loading: ${nullStats.null_actual_loading} (${((nullStats.null_actual_loading/nullStats.total_trips)*100).toFixed(1)}%)`);
    console.log(`  NULL Actual Unloading: ${nullStats.null_actual_unloading} (${((nullStats.null_actual_unloading/nullStats.total_trips)*100).toFixed(1)}%)`);

    // 4. Analyze complex trip patterns (A→B→A→COMPLETED→C)
    console.log('\n🔄 COMPLEX TRIP PATTERN ANALYSIS (A→B→A→COMPLETED→C)');
    console.log('-' .repeat(50));
    
    const complexPatterns = await client.query(`
      WITH trip_sequences AS (
        SELECT 
          a.truck_id,
          dt.truck_number,
          tl.assignment_id,
          a.assignment_code,
          tl.trip_number,
          tl.status,
          tl.created_at,
          a.loading_location_id,
          a.unloading_location_id,
          ll.name as loading_location,
          ul.name as unloading_location,
          tl.actual_loading_location_id,
          tl.actual_unloading_location_id,
          al.name as actual_loading_location,
          aul.name as actual_unloading_location,
          LAG(tl.status) OVER (PARTITION BY a.truck_id ORDER BY tl.created_at) as prev_status,
          LAG(a.unloading_location_id) OVER (PARTITION BY a.truck_id ORDER BY tl.created_at) as prev_unloading_location,
          LEAD(a.loading_location_id) OVER (PARTITION BY a.truck_id ORDER BY tl.created_at) as next_loading_location
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
        LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
        WHERE tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
      )
      SELECT 
        truck_number,
        assignment_code,
        trip_number,
        status,
        loading_location,
        unloading_location,
        actual_loading_location,
        actual_unloading_location,
        CASE 
          WHEN prev_status = 'trip_completed' AND status != 'trip_completed' THEN 'NEW_TRIP_AFTER_COMPLETION'
          WHEN actual_loading_location_id != loading_location_id THEN 'LOADING_LOCATION_DEVIATION'
          WHEN actual_unloading_location_id != unloading_location_id THEN 'UNLOADING_LOCATION_DEVIATION'
          ELSE 'NORMAL_PROGRESSION'
        END as pattern_type,
        created_at
      FROM trip_sequences
      ORDER BY truck_number, created_at
    `);
    
    console.log('Complex Trip Patterns Found:');
    complexPatterns.rows.forEach(pattern => {
      console.log(`  ${pattern.truck_number} Trip #${pattern.trip_number} (${pattern.status}):`);
      console.log(`    Assignment: ${pattern.assignment_code}`);
      console.log(`    Planned: ${pattern.loading_location} → ${pattern.unloading_location}`);
      console.log(`    Actual: ${pattern.actual_loading_location || 'NULL'} → ${pattern.actual_unloading_location || 'NULL'}`);
      console.log(`    Pattern: ${pattern.pattern_type}`);
      console.log(`    Time: ${pattern.created_at}`);
      console.log('');
    });

    // 5. Analyze database triggers and functions
    console.log('\n⚙️ DATABASE TRIGGERS AND FUNCTIONS ANALYSIS');
    console.log('-' .repeat(50));
    
    const triggers = await client.query(`
      SELECT 
        trigger_name,
        event_manipulation,
        event_object_table,
        action_timing,
        action_statement
      FROM information_schema.triggers
      WHERE trigger_schema = 'public'
      ORDER BY event_object_table, trigger_name
    `);
    
    console.log('Active Database Triggers:');
    triggers.rows.forEach(trigger => {
      console.log(`  ${trigger.trigger_name}:`);
      console.log(`    Table: ${trigger.event_object_table}`);
      console.log(`    Event: ${trigger.action_timing} ${trigger.event_manipulation}`);
      console.log(`    Action: ${trigger.action_statement.substring(0, 100)}...`);
      console.log('');
    });

    // 6. Analyze assignment notes JSON structure
    console.log('\n📝 ASSIGNMENT NOTES JSON STRUCTURE ANALYSIS');
    console.log('-' .repeat(50));
    
    const notesAnalysis = await client.query(`
      SELECT 
        assignment_code,
        notes,
        CASE 
          WHEN notes IS NULL THEN 'NULL'
          WHEN notes::text LIKE '%dynamic_assignment%' THEN 'DYNAMIC_ASSIGNMENT'
          WHEN notes::text LIKE '%auto_assignment%' THEN 'AUTO_ASSIGNMENT'
          WHEN notes::text LIKE '%manual%' THEN 'MANUAL_ASSIGNMENT'
          ELSE 'OTHER'
        END as notes_type,
        created_at
      FROM assignments
      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
      ORDER BY created_at DESC
    `);
    
    console.log('Assignment Notes Structure:');
    notesAnalysis.rows.forEach(assignment => {
      console.log(`  ${assignment.assignment_code} (${assignment.notes_type}):`);
      if (assignment.notes) {
        try {
          const notes = JSON.parse(assignment.notes);
          console.log(`    Creation Method: ${notes.creation_method || 'Not specified'}`);
          console.log(`    Route Discovery: ${notes.route_discovery ? 'Yes' : 'No'}`);
          if (notes.route_discovery) {
            console.log(`    Discovery Mode: ${notes.route_discovery.mode || 'Not specified'}`);
          }
        } catch (e) {
          console.log(`    Raw Notes: ${assignment.notes.substring(0, 100)}...`);
        }
      } else {
        console.log(`    Notes: NULL`);
      }
      console.log('');
    });

    // 7. Analyze database constraints
    console.log('\n🔒 DATABASE CONSTRAINTS ANALYSIS');
    console.log('-' .repeat(50));
    
    const constraints = await client.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        tc.constraint_type,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      LEFT JOIN information_schema.constraint_column_usage ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.table_schema = 'public'
        AND tc.table_name IN ('trip_logs', 'assignments')
      ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name
    `);
    
    console.log('Database Constraints:');
    constraints.rows.forEach(constraint => {
      console.log(`  ${constraint.table_name}.${constraint.constraint_name}:`);
      console.log(`    Type: ${constraint.constraint_type}`);
      console.log(`    Column: ${constraint.column_name || 'N/A'}`);
      if (constraint.foreign_table_name) {
        console.log(`    References: ${constraint.foreign_table_name}.${constraint.foreign_column_name}`);
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeDatabaseSchemaAndDataPopulation().catch(console.error);
