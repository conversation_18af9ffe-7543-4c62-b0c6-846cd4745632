const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeTrip1Status() {
  const client = await pool.connect();
  try {
    console.log('🔍 Analyzing Trip #1 Status Issue...');

    // Get Trip #1 detailed information
    const trip1Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time, tl.total_duration_minutes,
        tl.unloading_duration_minutes,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.notes,
        a.assignment_code, a.notes as assignment_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.id = 105
    `);

    if (trip1Result.rows.length === 0) {
      console.log('❌ Trip #1 (ID: 105) not found');
      return;
    }

    const trip1 = trip1Result.rows[0];
    console.log('\n📊 Trip #1 Current State Analysis:');
    console.log('=' .repeat(50));
    console.log(`Trip ID: ${trip1.id}`);
    console.log(`Trip Number: ${trip1.trip_number}`);
    console.log(`Assignment: ${trip1.assignment_code}`);
    console.log(`Current Status: ${trip1.status}`);
    console.log('');

    console.log('⏰ Timestamp Analysis:');
    console.log(`Loading Start: ${trip1.loading_start_time || 'NULL'} ${trip1.loading_start_time ? '✅' : '❌'}`);
    console.log(`Loading End: ${trip1.loading_end_time || 'NULL'} ${trip1.loading_end_time ? '✅' : '❌'}`);
    console.log(`Unloading Start: ${trip1.unloading_start_time || 'NULL'} ${trip1.unloading_start_time ? '✅' : '❌'}`);
    console.log(`Unloading End: ${trip1.unloading_end_time || 'NULL'} ${trip1.unloading_end_time ? '✅' : '❌'}`);
    console.log(`Trip Completed: ${trip1.trip_completed_time || 'NULL'} ${trip1.trip_completed_time ? '✅' : '❌'}`);
    console.log('');

    console.log('📍 Location Analysis:');
    console.log(`Actual Loading Location ID: ${trip1.actual_loading_location_id || 'NULL'}`);
    console.log(`Actual Unloading Location ID: ${trip1.actual_unloading_location_id || 'NULL'}`);
    console.log('');

    console.log('⏱️ Duration Analysis:');
    console.log(`Total Duration: ${trip1.total_duration_minutes || 'NULL'} minutes`);
    console.log(`Unloading Duration: ${trip1.unloading_duration_minutes || 'NULL'} minutes`);
    console.log('');

    // Analyze trip progression
    const hasLoadingPhase = !!(trip1.loading_start_time && trip1.loading_end_time);
    const hasUnloadingPhase = !!(trip1.unloading_start_time && trip1.unloading_end_time);
    const hasCompletion = !!trip1.trip_completed_time;

    console.log('🎯 Trip Progression Analysis:');
    console.log(`Loading Phase Complete: ${hasLoadingPhase ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Unloading Phase Complete: ${hasUnloadingPhase ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Trip Completion Time Set: ${hasCompletion ? 'Yes ✅' : 'No ❌'}`);
    console.log('');

    // Determine correct status based on actual progression
    let correctStatus = 'assigned';
    let statusReason = '';

    if (hasUnloadingPhase && hasLoadingPhase && hasCompletion) {
      correctStatus = 'trip_completed';
      statusReason = 'Trip completed all phases naturally';
    } else if (hasUnloadingPhase && !hasLoadingPhase) {
      correctStatus = 'unloading_end';
      statusReason = 'Trip completed unloading phase but never had loading phase (dynamic route discovery)';
    } else if (hasLoadingPhase && !hasUnloadingPhase) {
      correctStatus = 'loading_end';
      statusReason = 'Trip completed loading phase but never started unloading';
    } else if (trip1.unloading_start_time && !trip1.unloading_end_time) {
      correctStatus = 'unloading_start';
      statusReason = 'Trip started unloading but never completed unloading';
    } else if (trip1.loading_start_time && !trip1.loading_end_time) {
      correctStatus = 'loading_start';
      statusReason = 'Trip started loading but never completed loading';
    }

    console.log('🔍 Status Determination:');
    console.log(`Current Database Status: ${trip1.status}`);
    console.log(`Correct Status Should Be: ${correctStatus}`);
    console.log(`Reason: ${statusReason}`);
    console.log('');

    // Check if this is a dynamic assignment
    let isDynamicAssignment = false;
    let assignmentCreationMethod = 'unknown';
    if (trip1.assignment_notes) {
      try {
        const assignmentNotes = JSON.parse(trip1.assignment_notes);
        assignmentCreationMethod = assignmentNotes.creation_method || 'unknown';
        isDynamicAssignment = assignmentCreationMethod === 'dynamic_assignment';
      } catch (e) {
        console.log('❌ Failed to parse assignment notes');
      }
    }

    console.log('📝 Assignment Analysis:');
    console.log(`Assignment Creation Method: ${assignmentCreationMethod}`);
    console.log(`Is Dynamic Assignment: ${isDynamicAssignment ? 'Yes' : 'No'}`);
    console.log('');

    // Check trip notes for completion method
    let completionMethod = 'unknown';
    if (trip1.notes) {
      try {
        const tripNotes = JSON.parse(trip1.notes);
        completionMethod = tripNotes.completion_method || 'unknown';
        console.log('📋 Trip Notes Analysis:');
        console.log(`Completion Method: ${completionMethod}`);
        if (tripNotes.reset_reason) {
          console.log(`Reset Reason: ${tripNotes.reset_reason}`);
        }
        if (tripNotes.completion_location_name) {
          console.log(`Completion Location: ${tripNotes.completion_location_name}`);
        }
      } catch (e) {
        console.log('❌ Failed to parse trip notes');
      }
    }
    console.log('');

    // Issue identification
    console.log('⚠️ Issue Identification:');
    if (trip1.status === 'trip_completed' && correctStatus !== 'trip_completed') {
      console.log('❌ ISSUE FOUND: Trip shows trip_completed but should be ' + correctStatus);
      console.log('❌ This is the same pattern as Trip #2 but for unloading phase');
      console.log('❌ Trip completed unloading but never had loading phase');
      console.log('❌ Status should reflect actual progression, not forced completion');
    } else if (trip1.status === correctStatus) {
      console.log('✅ Status is correct');
    } else {
      console.log(`⚠️ Status mismatch: ${trip1.status} vs ${correctStatus}`);
    }

    console.log('');
    console.log('🔧 Required Fix:');
    console.log(`1. Change status from "${trip1.status}" to "${correctStatus}"`);
    console.log(`2. Remove trip_completed_time (should be NULL for ${correctStatus})`);
    console.log(`3. Recalculate duration based on unloading phase only`);
    console.log(`4. Add correction notes for audit trail`);

    // Calculate correct duration for unloading-only trip
    if (trip1.unloading_start_time && trip1.unloading_end_time) {
      const unloadingDuration = Math.round(
        (new Date(trip1.unloading_end_time) - new Date(trip1.unloading_start_time)) / (1000 * 60)
      );
      console.log(`5. Correct duration should be: ${unloadingDuration} minutes (unloading phase only)`);
    }

  } catch (error) {
    console.error('❌ Error analyzing Trip #1 status:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeTrip1Status().catch(console.error);
