const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function investigateTripMonitoringDataIntegrity() {
  const client = await pool.connect();
  try {
    console.log('🔍 TRIP MONITORING DASHBOARD DATA INTEGRITY INVESTIGATION');
    console.log('=' .repeat(80));

    // 1. Analyze the exact data that the frontend receives
    console.log('\n📋 FRONTEND DATA ANALYSIS (Exact API Response)');
    console.log('-' .repeat(60));
    
    const frontendDataQuery = `
      SELECT
        t.id, t.trip_number, t.status, t.loading_start_time, t.loading_end_time,
        t.unloading_start_time, t.unloading_end_time, t.trip_completed_time,
        t.is_exception, t.exception_reason, t.total_duration_minutes,
        t.loading_duration_minutes, t.travel_duration_minutes, t.unloading_duration_minutes,
        t.notes, t.created_at, t.updated_at,
        a.id as assignment_id, a.assigned_date, a.notes as assignment_notes,
        tr.id as truck_id, tr.truck_number, tr.license_plate,
        d.id as driver_id, d.employee_id, d.full_name as driver_name,
        ll.id as loading_location_id, ll.name as loading_location,
        ul.id as unloading_location_id, ul.name as unloading_location
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks tr ON a.truck_id = tr.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON COALESCE(t.actual_loading_location_id, a.loading_location_id) = ll.id
      LEFT JOIN locations ul ON COALESCE(t.actual_unloading_location_id, a.unloading_location_id) = ul.id
      WHERE t.created_at >= CURRENT_DATE - INTERVAL '2 days'
      ORDER BY t.created_at DESC
    `;
    
    const frontendData = await client.query(frontendDataQuery);
    
    console.log(`Found ${frontendData.rows.length} trips in frontend data:`);
    
    // Analyze each trip for data integrity issues
    const tripNumberCounts = {};
    const assignmentUsage = {};
    let dynamicRouteCount = 0;
    let traditionalRouteCount = 0;
    
    frontendData.rows.forEach(trip => {
      // Count trip numbers per truck
      const truckKey = trip.truck_number;
      if (!tripNumberCounts[truckKey]) {
        tripNumberCounts[truckKey] = {};
      }
      if (!tripNumberCounts[truckKey][trip.trip_number]) {
        tripNumberCounts[truckKey][trip.trip_number] = [];
      }
      tripNumberCounts[truckKey][trip.trip_number].push({
        trip_id: trip.id,
        assignment_id: trip.assignment_id,
        created_at: trip.created_at
      });
      
      // Count assignment usage
      if (!assignmentUsage[trip.assignment_id]) {
        assignmentUsage[trip.assignment_id] = [];
      }
      assignmentUsage[trip.assignment_id].push({
        trip_id: trip.id,
        trip_number: trip.trip_number,
        created_at: trip.created_at
      });
      
      // Analyze Dynamic Route labeling
      let isDynamic = false;
      try {
        if (trip.assignment_notes) {
          const notes = JSON.parse(trip.assignment_notes);
          isDynamic = notes.creation_method === 'dynamic_assignment';
        }
      } catch (e) {
        // Ignore parsing errors
      }
      
      if (isDynamic) {
        dynamicRouteCount++;
      } else {
        traditionalRouteCount++;
      }
      
      console.log(`\n  Trip #${trip.trip_number} (ID: ${trip.id}):`);
      console.log(`    Truck: ${trip.truck_number}`);
      console.log(`    Assignment: ${trip.assignment_id}`);
      console.log(`    Route: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`    Status: ${trip.status}`);
      console.log(`    Created: ${trip.created_at}`);
      console.log(`    Route Type: ${isDynamic ? '🔄 Dynamic Route' : '📍 Traditional Route'}`);
      
      if (trip.assignment_notes) {
        try {
          const notes = JSON.parse(trip.assignment_notes);
          console.log(`    Assignment Notes: creation_method=${notes.creation_method || 'not_set'}`);
        } catch (e) {
          console.log(`    Assignment Notes: Invalid JSON`);
        }
      } else {
        console.log(`    Assignment Notes: NULL`);
      }
    });

    // 2. Analyze duplicate trip numbers
    console.log('\n\n🔍 DUPLICATE TRIP NUMBER ANALYSIS');
    console.log('-' .repeat(60));
    
    let duplicatesFound = false;
    Object.keys(tripNumberCounts).forEach(truck => {
      Object.keys(tripNumberCounts[truck]).forEach(tripNumber => {
        const occurrences = tripNumberCounts[truck][tripNumber];
        if (occurrences.length > 1) {
          duplicatesFound = true;
          console.log(`\n⚠️ DUPLICATE FOUND: ${truck} Trip #${tripNumber} (${occurrences.length} occurrences)`);
          occurrences.forEach((occurrence, index) => {
            console.log(`    ${index + 1}. Trip ID: ${occurrence.trip_id}, Assignment: ${occurrence.assignment_id}, Created: ${occurrence.created_at}`);
          });
        }
      });
    });
    
    if (!duplicatesFound) {
      console.log('✅ No duplicate trip numbers found within same truck');
    }

    // 3. Analyze assignment reuse patterns
    console.log('\n\n🔍 ASSIGNMENT REUSE PATTERN ANALYSIS');
    console.log('-' .repeat(60));
    
    let multipleTripsPerAssignment = false;
    Object.keys(assignmentUsage).forEach(assignmentId => {
      const trips = assignmentUsage[assignmentId];
      if (trips.length > 1) {
        multipleTripsPerAssignment = true;
        console.log(`\n📋 Assignment ${assignmentId} has ${trips.length} trips:`);
        trips.forEach((trip, index) => {
          console.log(`    ${index + 1}. Trip #${trip.trip_number} (ID: ${trip.trip_id}) - ${trip.created_at}`);
        });
      }
    });
    
    if (!multipleTripsPerAssignment) {
      console.log('✅ Each assignment has only one trip (no reuse detected)');
    }

    // 4. Analyze "Dynamic Route" labeling accuracy
    console.log('\n\n🔍 "DYNAMIC ROUTE" LABELING ACCURACY ANALYSIS');
    console.log('-' .repeat(60));
    
    console.log(`Total trips analyzed: ${frontendData.rows.length}`);
    console.log(`Dynamic Routes: ${dynamicRouteCount} (${((dynamicRouteCount/frontendData.rows.length)*100).toFixed(1)}%)`);
    console.log(`Traditional Routes: ${traditionalRouteCount} (${((traditionalRouteCount/frontendData.rows.length)*100).toFixed(1)}%)`);

    // 5. Specific Trip #1 vs Trip #2 analysis
    console.log('\n\n🎯 SPECIFIC TRIP #1 vs TRIP #2 ANALYSIS');
    console.log('-' .repeat(60));
    
    const specificAnalysis = await client.query(`
      SELECT 
        t.id as trip_id,
        t.trip_number,
        t.assignment_id,
        a.assignment_code,
        a.notes as assignment_notes,
        t.status,
        t.created_at,
        tr.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        -- Check if this assignment was created dynamically
        CASE 
          WHEN a.notes::text LIKE '%dynamic_assignment%' THEN true
          ELSE false
        END as is_dynamic_assignment
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks tr ON a.truck_id = tr.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE t.trip_number IN (1, 2)
        AND t.created_at >= '2025-07-02 00:00:00'
        AND t.created_at < '2025-07-03 00:00:00'
      ORDER BY t.created_at ASC
    `);
    
    console.log('Trip #1 vs Trip #2 Comparison:');
    specificAnalysis.rows.forEach(trip => {
      console.log(`\n  Trip #${trip.trip_number} (ID: ${trip.trip_id}):`);
      console.log(`    Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
      console.log(`    Truck: ${trip.truck_number}`);
      console.log(`    Route: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`    Status: ${trip.status}`);
      console.log(`    Created: ${trip.created_at}`);
      console.log(`    Is Dynamic Assignment: ${trip.is_dynamic_assignment ? 'YES 🔄' : 'NO 📍'}`);
      
      if (trip.assignment_notes) {
        try {
          const notes = JSON.parse(trip.assignment_notes);
          console.log(`    Creation Method: ${notes.creation_method || 'not_specified'}`);
          if (notes.route_discovery) {
            console.log(`    Route Discovery: ${notes.route_discovery.mode || 'not_specified'}`);
          }
        } catch (e) {
          console.log(`    Assignment Notes: Invalid JSON - ${trip.assignment_notes.substring(0, 50)}...`);
        }
      } else {
        console.log(`    Assignment Notes: NULL`);
      }
      
      // This is the key analysis - why does Trip #2 show as Dynamic Route?
      if (trip.trip_number === 2 && trip.is_dynamic_assignment) {
        console.log(`    ⚠️ ISSUE IDENTIFIED: Trip #2 shows as Dynamic Route because assignment ${trip.assignment_id} has creation_method='dynamic_assignment'`);
        console.log(`    🔍 ROOT CAUSE: System created new dynamic assignment instead of reusing existing assignment`);
      }
    });

    // 6. Frontend rendering simulation
    console.log('\n\n🖥️ FRONTEND RENDERING SIMULATION');
    console.log('-' .repeat(60));
    
    console.log('Simulating renderDynamicRoute() function logic:');
    frontendData.rows.slice(0, 5).forEach(trip => {
      // Simulate the frontend logic
      const isDynamicAssignment = trip.assignment_notes &&
        JSON.parse(trip.assignment_notes || '{}').creation_method === 'dynamic_assignment';
      
      console.log(`\n  Trip #${trip.trip_number} (${trip.truck_number}):`);
      console.log(`    assignment_notes: ${trip.assignment_notes ? 'Present' : 'NULL'}`);
      
      if (trip.assignment_notes) {
        try {
          const notes = JSON.parse(trip.assignment_notes);
          console.log(`    creation_method: ${notes.creation_method || 'undefined'}`);
        } catch (e) {
          console.log(`    JSON Parse Error: ${e.message}`);
        }
      }
      
      console.log(`    isDynamicAssignment: ${isDynamicAssignment}`);
      console.log(`    Frontend Display: ${isDynamicAssignment ? '🔄 Dynamic Route' : '📍 Traditional Route'}`);
    });

    console.log('\n\n🎯 DATA INTEGRITY INVESTIGATION SUMMARY');
    console.log('=' .repeat(80));
    console.log(`✅ Total trips analyzed: ${frontendData.rows.length}`);
    console.log(`${duplicatesFound ? '⚠️' : '✅'} Duplicate trip numbers: ${duplicatesFound ? 'FOUND' : 'None detected'}`);
    console.log(`${multipleTripsPerAssignment ? '⚠️' : '✅'} Assignment reuse: ${multipleTripsPerAssignment ? 'DETECTED' : 'None detected'}`);
    console.log(`📊 Dynamic Route labeling: ${dynamicRouteCount}/${frontendData.rows.length} trips`);
    console.log(`🔍 Root cause: Assignment creation logic creates new assignments instead of reusing existing ones`);
    console.log(`💡 Solution: Enhanced assignment validation logic to find and reuse existing assignments`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

investigateTripMonitoringDataIntegrity().catch(console.error);
