const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeAssignmentIssues() {
  const client = await pool.connect();
  try {
    console.log('🔍 Analyzing Assignment and Trip Number Issues...\n');

    // Get assignments and their trips
    const result = await client.query(`
      SELECT 
        a.id as assignment_id,
        a.assignment_code,
        a.notes as assignment_notes,
        a.created_at as assignment_created,
        dt.truck_number,
        COUNT(tl.id) as trip_count,
        STRING_AGG(DISTINCT tl.trip_number::text, ', ' ORDER BY tl.trip_number::text) as trip_numbers,
        STRING_AGG(DISTINCT tl.status::text, ', ') as trip_statuses
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      WHERE a.created_at >= CURRENT_DATE - INTERVAL '1 day'
      GROUP BY a.id, a.assignment_code, a.notes, a.created_at, dt.truck_number
      ORDER BY a.created_at DESC
    `);

    console.log('📋 ASSIGNMENTS AND THEIR TRIPS:');
    console.log('=' .repeat(80));
    
    result.rows.forEach(row => {
      let isDynamic = false;
      let creationMethod = 'unknown';
      
      if (row.assignment_notes) {
        try {
          const notes = JSON.parse(row.assignment_notes);
          creationMethod = notes.creation_method || 'unknown';
          isDynamic = creationMethod === 'dynamic_assignment';
        } catch (e) {
          creationMethod = 'parse_error';
        }
      }
      
      console.log(`\nAssignment: ${row.assignment_code} (ID: ${row.assignment_id})`);
      console.log(`  Truck: ${row.truck_number}`);
      console.log(`  Type: ${creationMethod} ${isDynamic ? '🔄' : '📍'}`);
      console.log(`  Created: ${row.assignment_created}`);
      console.log(`  Trip Count: ${row.trip_count}`);
      console.log(`  Trip Numbers: ${row.trip_numbers || 'None'}`);
      console.log(`  Trip Statuses: ${row.trip_statuses || 'None'}`);
    });

    // Check for duplicate trip numbers across different assignments
    const duplicateCheck = await client.query(`
      SELECT 
        tl.trip_number,
        COUNT(*) as occurrence_count,
        STRING_AGG(DISTINCT a.assignment_code, ', ') as assignments,
        STRING_AGG(DISTINCT dt.truck_number, ', ') as trucks
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      GROUP BY tl.trip_number
      HAVING COUNT(*) > 1
      ORDER BY tl.trip_number
    `);

    if (duplicateCheck.rows.length > 0) {
      console.log('\n⚠️ DUPLICATE TRIP NUMBERS FOUND:');
      console.log('=' .repeat(50));
      duplicateCheck.rows.forEach(row => {
        console.log(`Trip #${row.trip_number}: ${row.occurrence_count} occurrences`);
        console.log(`  Assignments: ${row.assignments}`);
        console.log(`  Trucks: ${row.trucks}`);
      });
    } else {
      console.log('\n✅ No duplicate trip numbers found across different assignments');
    }

    // Analyze the specific Trip #1 vs Trip #2 issue
    console.log('\n🎯 ANALYZING TRIP #1 vs TRIP #2 ISSUE:');
    console.log('=' .repeat(60));
    
    const tripAnalysis = await client.query(`
      SELECT 
        tl.id as trip_id,
        tl.trip_number,
        tl.assignment_id,
        a.assignment_code,
        a.notes as assignment_notes,
        dt.truck_number,
        tl.status,
        tl.created_at,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.created_at >= '2025-07-02 00:00:00'
        AND tl.created_at < '2025-07-03 00:00:00'
      ORDER BY tl.created_at ASC
    `);

    tripAnalysis.rows.forEach(trip => {
      let isDynamic = false;
      let creationMethod = 'unknown';
      
      if (trip.assignment_notes) {
        try {
          const notes = JSON.parse(trip.assignment_notes);
          creationMethod = notes.creation_method || 'unknown';
          isDynamic = creationMethod === 'dynamic_assignment';
        } catch (e) {
          creationMethod = 'parse_error';
        }
      }
      
      console.log(`\nTrip #${trip.trip_number} (ID: ${trip.trip_id})`);
      console.log(`  Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
      console.log(`  Truck: ${trip.truck_number}`);
      console.log(`  Route: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`  Status: ${trip.status}`);
      console.log(`  Created: ${trip.created_at}`);
      console.log(`  Assignment Type: ${creationMethod} ${isDynamic ? '🔄 Dynamic Route' : '📍 Traditional Route'}`);
      
      // This is the key issue - why does Trip #2 show "Dynamic Route" when it should reference existing assignment?
      if (trip.trip_number === 2 && isDynamic) {
        console.log(`  ⚠️ ISSUE: Trip #2 shows as Dynamic Route but should reference existing assignment!`);
      }
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeAssignmentIssues().catch(console.error);
