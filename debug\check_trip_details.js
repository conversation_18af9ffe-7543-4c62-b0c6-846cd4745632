const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function checkTripDetails() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking detailed trip information...');

    const result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.created_at, tl.updated_at,
        a.notes as assignment_notes,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `);

    if (result.rows.length === 0) {
      console.log('❌ No recent trips found');
      return;
    }

    const trip = result.rows[0];
    console.log('📊 Trip Details:');
    console.log(`  Trip ID: ${trip.id}`);
    console.log(`  Trip Number: ${trip.trip_number}`);
    console.log(`  Status: ${trip.status}`);
    console.log(`  Assignment ID: ${trip.assignment_id}`);
    console.log('');
    
    console.log('⏰ Timestamps:');
    console.log(`  Loading Start: ${trip.loading_start_time || 'NULL'}`);
    console.log(`  Loading End: ${trip.loading_end_time || 'NULL'}`);
    console.log(`  Unloading Start: ${trip.unloading_start_time || 'NULL'}`);
    console.log(`  Unloading End: ${trip.unloading_end_time || 'NULL'}`);
    console.log(`  Trip Completed: ${trip.trip_completed_time || 'NULL'}`);
    console.log('');
    
    console.log('📍 Locations:');
    console.log(`  Assigned Route: ${trip.assigned_loading_location} → ${trip.assigned_unloading_location}`);
    console.log(`  Actual Route: ${trip.actual_loading_location || 'None'} → ${trip.actual_unloading_location || 'None'}`);
    console.log('');
    
    console.log('📝 Assignment Notes:');
    if (trip.assignment_notes) {
      try {
        const notes = JSON.parse(trip.assignment_notes);
        console.log(`  Creation Method: ${notes.creation_method || 'Unknown'}`);
        console.log(`  Route Discovery: ${JSON.stringify(notes.route_discovery || {}, null, 2)}`);
      } catch (e) {
        console.log(`  Raw Notes: ${trip.assignment_notes}`);
      }
    } else {
      console.log('  No assignment notes');
    }
    console.log('');
    
    // Validation check
    console.log('✅ Validation Check:');
    const hasLoadingStart = !!trip.loading_start_time;
    const hasLoadingEnd = !!trip.loading_end_time;
    const hasUnloadingStart = !!trip.unloading_start_time;
    const hasUnloadingEnd = !!trip.unloading_end_time;
    
    console.log(`  Loading Start: ${hasLoadingStart ? '✅' : '❌'}`);
    console.log(`  Loading End: ${hasLoadingEnd ? '✅' : '❌'}`);
    console.log(`  Unloading Start: ${hasUnloadingStart ? '✅' : '❌'}`);
    console.log(`  Unloading End: ${hasUnloadingEnd ? '✅' : '❌'}`);
    
    // Check what validation would fail
    const missingSteps = [];
    if (!hasLoadingStart) missingSteps.push("loading start");
    if (!hasLoadingEnd) missingSteps.push("loading end");
    if (!hasUnloadingStart) missingSteps.push("unloading start");
    if (!hasUnloadingEnd) missingSteps.push("unloading end");
    
    if (missingSteps.length > 0) {
      console.log(`  ❌ Missing Steps: ${missingSteps.join(", ")}`);
    } else {
      console.log(`  ✅ All steps completed`);
    }

  } finally {
    client.release();
    await pool.end();
  }
}

checkTripDetails().catch(console.error);
