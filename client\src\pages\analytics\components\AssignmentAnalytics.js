import React from 'react';

const AssignmentAnalytics = ({ data, loading, dateRange }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-32 rounded-lg"></div>
          ))}
        </div>
        <div className="bg-secondary-200 h-64 rounded-lg"></div>
      </div>
    );
  }

  // Enhanced error handling - Use REAL data from API with proper fallbacks
  const assignmentData = data || {
    summary: {
      total: 0,
      active: 0,
      completed: 0,
      autoCreated: 0,
      completionRate: '0.0'
    },
    recent: [],
    trends: [],
    performance: {
      avgCreationTime: 0,
      successRate: 0,
      autoAssignmentRate: 0
    }
  };

  // Show different messages based on data availability
  if (!data) {
    return (
      <div className="text-center py-12">
        <svg className="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No Assignment Data Available</h3>
        <p className="text-secondary-500">Assignment analytics will appear here once there are assignments.</p>
        <p className="text-secondary-400 text-sm mt-2">Check your network connection or try refreshing the page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards - REAL DATA with error handling */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium text-sm">{assignmentData.summary?.total || 0}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Total Assignments</p>
              <p className="text-xs text-secondary-500">All time</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{assignmentData.summary?.active || 0}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Active Assignments</p>
              <p className="text-xs text-secondary-500">Currently in progress</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-medium text-sm">{assignmentData.summary?.autoCreated || 0}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Auto-Created</p>
              <p className="text-xs text-secondary-500">By AutoAssignmentCreator</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-medium text-sm">{assignmentData.summary?.completionRate || '0.0'}%</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Completion Rate</p>
              <p className="text-xs text-secondary-500">Successfully completed</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Assignments - REAL DATA with enhanced error handling */}
      <div className="bg-white border border-secondary-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-secondary-900 mb-4">Recent Assignments</h4>
        {assignmentData.recent && Array.isArray(assignmentData.recent) && assignmentData.recent.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-secondary-200">
              <thead className="bg-secondary-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Assignment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Truck
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Route
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Created
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-secondary-200">
                {assignmentData.recent.slice(0, 10).map((assignment, index) => (
                  <tr key={assignment.id || index} className="hover:bg-secondary-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary-900">
                      {assignment.assignment_code || `ASG-${assignment.id}`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                      {assignment.truck_number || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                      <div className="flex flex-col">
                        <span className="font-medium">{assignment.loading_location_name || 'Unknown'}</span>
                        <span className="text-secondary-500">→ {assignment.unloading_location_name || 'Unknown'}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        assignment.status === 'completed' ? 'bg-green-100 text-green-800' :
                        assignment.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                        assignment.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {assignment.status?.replace('_', ' ').toUpperCase() || 'UNKNOWN'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        assignment.is_auto_created ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {assignment.is_auto_created ? '🤖 Auto' : '👤 Manual'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      {assignment.created_at ? new Date(assignment.created_at).toLocaleDateString() : 'Unknown'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <svg className="w-8 h-8 text-secondary-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-secondary-500 text-sm">No recent assignments found</p>
            <p className="text-secondary-400 text-xs mt-1">Assignments will appear here once created</p>
          </div>
        )}
      </div>

      {/* Assignment Performance Metrics - REAL DATA with error handling */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-blue-900 mb-4">Assignment Performance</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {assignmentData.performance?.successRate || 0}%
            </div>
            <div className="text-sm text-blue-700">Success Rate</div>
            <div className="text-xs text-blue-600 mt-1">Target: &gt;95%</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {assignmentData.performance?.avgCreationTime || 0}ms
            </div>
            <div className="text-sm text-blue-700">Avg Creation Time</div>
            <div className="text-xs text-blue-600 mt-1">Target: &lt;300ms</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {assignmentData.performance?.autoAssignmentRate || 0}%
            </div>
            <div className="text-sm text-blue-700">Auto-Assignment Rate</div>
            <div className="text-xs text-blue-600 mt-1">Intelligent creation</div>
          </div>
        </div>
        <p className="text-sm text-blue-700 mt-4 text-center">
          ✅ All assignment data is fetched live from the database • Updated: {new Date().toLocaleTimeString()}
        </p>
      </div>

      {/* Assignment Trends Chart Placeholder */}
      <div className="bg-white border border-secondary-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-secondary-900 mb-4">Assignment Creation Trends</h4>
        <div className="h-64 flex items-center justify-center bg-secondary-50 rounded-lg">
          <div className="text-center">
            <svg className="w-12 h-12 text-secondary-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2 2z" />
            </svg>
            <p className="text-secondary-500 text-sm">Assignment trends chart</p>
            <p className="text-secondary-400 text-xs mt-1">Visual analytics coming soon</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignmentAnalytics;
