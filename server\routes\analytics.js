const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');

// @route   GET /api/analytics/dashboard
// @desc    Get dashboard analytics data
// @access  Private
router.get('/dashboard', auth, async (req, res) => {
  try {
    // Get fleet overview
    const fleetQuery = `
      SELECT
        (SELECT COUNT(*) FROM dump_trucks) as total_trucks,
        (SELECT COUNT(*) FROM dump_trucks WHERE status = 'active') as active_trucks,
        (SELECT COUNT(*) FROM drivers) as total_drivers,
        (SELECT COUNT(*) FROM drivers WHERE status = 'active') as active_drivers,
        (SELECT COUNT(*) FROM locations) as total_locations,
        (SELECT COUNT(*) FROM locations WHERE status = 'active') as active_locations,
        (SELECT COUNT(*) FROM assignments) as total_assignments,
        (SELECT COUNT(*) FROM assignments WHERE status IN ('assigned', 'in_progress')) as active_assignments
    `;

    // Get trip statistics
    const tripsQuery = `
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today_trips,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE AND status = 'trip_completed' THEN 1 END) as today_completed,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as weekly_trips,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('week', CURRENT_DATE) AND status = 'trip_completed' THEN 1 END) as weekly_completed,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as monthly_trips,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('month', CURRENT_DATE) AND status = 'trip_completed' THEN 1 END) as monthly_completed,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as total_completed
      FROM trip_logs
    `;

    // Get performance metrics
    const performanceQuery = `
      SELECT 
        AVG(total_duration_minutes) as avg_trip_time,
        AVG(loading_duration_minutes) as avg_loading_time,
        AVG(travel_duration_minutes) as avg_travel_time,
        AVG(unloading_duration_minutes) as avg_unloading_time,
        (COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as completion_rate,
        (COUNT(CASE WHEN is_exception = false AND status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN status = 'trip_completed' THEN 1 END), 0)) as on_time_rate,
        (COUNT(CASE WHEN is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as exception_rate
      FROM trip_logs
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `;

    const [fleetResult, tripsResult, performanceResult] = await Promise.all([
      query(fleetQuery),
      query(tripsQuery),
      query(performanceQuery)
    ]);

    const fleet = fleetResult.rows[0];
    const trips = tripsResult.rows[0];
    const performance = performanceResult.rows[0];

    // Format the response
    const dashboardData = {
      fleet: {
        totalTrucks: parseInt(fleet.total_trucks) || 0,
        activeTrucks: parseInt(fleet.active_trucks) || 0,
        totalDrivers: parseInt(fleet.total_drivers) || 0,
        activeDrivers: parseInt(fleet.active_drivers) || 0,
        totalLocations: parseInt(fleet.total_locations) || 0,
        activeLocations: parseInt(fleet.active_locations) || 0,
        totalAssignments: parseInt(fleet.total_assignments) || 0,
        activeAssignments: parseInt(fleet.active_assignments) || 0
      },
      trips: {
        todayTrips: parseInt(trips.today_trips) || 0,
        todayCompleted: parseInt(trips.today_completed) || 0,
        weeklyTrips: parseInt(trips.weekly_trips) || 0,
        weeklyCompleted: parseInt(trips.weekly_completed) || 0,
        monthlyTrips: parseInt(trips.monthly_trips) || 0,
        monthlyCompleted: parseInt(trips.monthly_completed) || 0,
        totalTrips: parseInt(trips.total_trips) || 0,
        totalCompleted: parseInt(trips.total_completed) || 0
      },
      performance: {
        avgTripTime: performance.avg_trip_time ? Math.round(parseFloat(performance.avg_trip_time)) : 0,
        avgLoadingTime: performance.avg_loading_time ? Math.round(parseFloat(performance.avg_loading_time)) : 0,
        avgTravelTime: performance.avg_travel_time ? Math.round(parseFloat(performance.avg_travel_time)) : 0,
        avgUnloadingTime: performance.avg_unloading_time ? Math.round(parseFloat(performance.avg_unloading_time)) : 0,
        completionRate: performance.completion_rate ? parseFloat(performance.completion_rate).toFixed(1) : '0.0',
        onTimeRate: performance.on_time_rate ? parseFloat(performance.on_time_rate).toFixed(1) : '0.0',
        exceptionRate: performance.exception_rate ? parseFloat(performance.exception_rate).toFixed(1) : '0.0'
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Get dashboard analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve dashboard analytics'
    });
  }
});

// @route   GET /api/analytics/assignments
// @desc    Get assignment analytics data
// @access  Private
router.get('/assignments', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    // Build date filter
    let dateFilter = '';
    if (start_date && end_date) {
      dateFilter = `WHERE a.created_at >= '${start_date}' AND a.created_at <= '${end_date}'`;
    } else {
      dateFilter = `WHERE a.created_at >= CURRENT_DATE - INTERVAL '30 days'`;
    }

    // Get assignment summary statistics
    const summaryQuery = `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN a.status = 'assigned' THEN 1 END) as active,
        COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN a.notes::text LIKE '%auto_assignment%' THEN 1 END) as auto_created,
        ROUND(
          (COUNT(CASE WHEN a.status = 'completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 1
        ) as completion_rate
      FROM assignments a
      ${dateFilter}
    `;

    // Get recent assignments
    const recentQuery = `
      SELECT
        a.id,
        a.assignment_code,
        a.status,
        a.created_at,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        CASE WHEN a.notes::text LIKE '%auto_assignment%' THEN true ELSE false END as is_auto_created
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      ${dateFilter}
      ORDER BY a.created_at DESC
      LIMIT 20
    `;

    // Get performance metrics
    const performanceQuery = `
      SELECT
        AVG(EXTRACT(EPOCH FROM (a.updated_at - a.created_at))/1000) as avg_creation_time,
        ROUND(
          (COUNT(CASE WHEN a.status IN ('assigned', 'completed') THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 1
        ) as success_rate,
        ROUND(
          (COUNT(CASE WHEN a.notes::text LIKE '%auto_assignment%' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 1
        ) as auto_assignment_rate
      FROM assignments a
      ${dateFilter}
    `;

    const [summaryResult, recentResult, performanceResult] = await Promise.all([
      query(summaryQuery),
      query(recentQuery),
      query(performanceQuery)
    ]);

    const summary = summaryResult.rows[0];
    const recent = recentResult.rows;
    const performance = performanceResult.rows[0];

    res.json({
      success: true,
      data: {
        summary: {
          total: parseInt(summary.total) || 0,
          active: parseInt(summary.active) || 0,
          completed: parseInt(summary.completed) || 0,
          autoCreated: parseInt(summary.auto_created) || 0,
          completionRate: summary.completion_rate || '0.0'
        },
        recent: recent,
        trends: [], // Placeholder for future trend analysis
        performance: {
          avgCreationTime: Math.round(performance.avg_creation_time) || 0,
          successRate: performance.success_rate || 0,
          autoAssignmentRate: performance.auto_assignment_rate || 0
        }
      }
    });

  } catch (error) {
    console.error('Error fetching assignment analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assignment analytics data'
    });
  }
});

// @route   GET /api/analytics/performance
// @desc    Get performance analytics data
// @access  Private
router.get('/performance', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Daily performance trends
    const trendsQuery = `
      SELECT 
        DATE(tl.created_at) as date,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        AVG(tl.total_duration_minutes) as avg_duration,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exceptions
      FROM trip_logs tl
      ${dateFilter}
      GROUP BY DATE(tl.created_at)
      ORDER BY DATE(tl.created_at)
    `;

    // Truck utilization
    const truckUtilizationQuery = `
      SELECT 
        dt.truck_number,
        dt.id as truck_id,
        COUNT(tl.id) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as completion_rate
      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      ${dateFilter.replace('tl.created_at', 'COALESCE(tl.created_at, a.created_at)')}
      GROUP BY dt.id, dt.truck_number
      ORDER BY trip_count DESC
      LIMIT 10
    `;

    // Driver performance
    const driverPerformanceQuery = `
      SELECT 
        d.full_name,
        d.employee_id,
        COUNT(tl.id) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as completion_rate
      FROM drivers d
      LEFT JOIN assignments a ON d.id = a.driver_id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      ${dateFilter.replace('tl.created_at', 'COALESCE(tl.created_at, a.created_at)')}
      GROUP BY d.id, d.full_name, d.employee_id
      ORDER BY trip_count DESC
      LIMIT 10
    `;

    const [trendsResult, truckUtilResult, driverPerfResult] = await Promise.all([
      query(trendsQuery, queryParams),
      query(truckUtilizationQuery, queryParams),
      query(driverPerformanceQuery, queryParams)
    ]);

    const performanceData = {
      trends: trendsResult.rows.map(row => ({
        date: row.date,
        totalTrips: parseInt(row.total_trips) || 0,
        completedTrips: parseInt(row.completed_trips) || 0,
        avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        exceptions: parseInt(row.exceptions) || 0
      })),
      truckUtilization: truckUtilResult.rows.map(row => ({
        truckNumber: row.truck_number,
        truckId: parseInt(row.truck_id),
        tripCount: parseInt(row.trip_count) || 0,
        avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0'
      })),
      driverPerformance: driverPerfResult.rows.map(row => ({
        driverName: row.full_name,
        employeeId: row.employee_id,
        tripCount: parseInt(row.trip_count) || 0,
        avgDuration: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0'
      }))
    };

    res.json({
      success: true,
      data: performanceData
    });

  } catch (error) {
    console.error('Get performance analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve performance analytics'
    });
  }
});

// @route   GET /api/analytics/exceptions
// @desc    Get exceptions analytics data
// @access  Private
router.get('/exceptions', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Exception summary
    const summaryQuery = `
      SELECT 
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as total_exceptions,
        COUNT(CASE WHEN tl.is_exception = true AND tl.exception_approved_by IS NULL THEN 1 END) as pending_exceptions,
        COUNT(CASE WHEN tl.is_exception = true AND tl.exception_approved_by IS NOT NULL THEN 1 END) as resolved_exceptions,
        (COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as exception_rate
      FROM trip_logs tl
      ${dateFilter}
    `;

    // Exception types (simulated as we don't have exception types in schema)
    const exceptionsQuery = `
      SELECT 
        tl.id,
        tl.trip_number,
        tl.exception_reason,
        tl.is_exception,
        tl.exception_approved_by,
        tl.exception_approved_at,
        tl.created_at,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN drivers d ON a.driver_id = d.id
      ${dateFilter} AND tl.is_exception = true
      ORDER BY tl.created_at DESC
      LIMIT 50
    `;

    // Daily exception trends
    const trendsQuery = `
      SELECT 
        DATE(tl.created_at) as date,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count
      FROM trip_logs tl
      ${dateFilter}
      GROUP BY DATE(tl.created_at)
      ORDER BY DATE(tl.created_at)
    `;

    const [summaryResult, exceptionsResult, trendsResult] = await Promise.all([
      query(summaryQuery, queryParams),
      query(exceptionsQuery, queryParams),
      query(trendsQuery, queryParams)
    ]);

    const summary = summaryResult.rows[0];

    const exceptionsData = {
      summary: {
        total: parseInt(summary.total_exceptions) || 0,
        pending: parseInt(summary.pending_exceptions) || 0,
        resolved: parseInt(summary.resolved_exceptions) || 0,
        rate: summary.exception_rate ? parseFloat(summary.exception_rate).toFixed(1) : '0.0'
      },
      recent: exceptionsResult.rows.map(row => ({
        id: row.id,
        tripNumber: row.trip_number,
        reason: row.exception_reason || 'No reason provided',
        truckNumber: row.truck_number,
        driverName: row.driver_name,
        employeeId: row.employee_id,
        status: row.exception_approved_by ? 'resolved' : 'pending',
        createdAt: row.created_at,
        resolvedAt: row.exception_approved_at
      })),
      trends: trendsResult.rows.map(row => ({
        date: row.date,
        count: parseInt(row.exception_count) || 0
      }))
    };

    res.json({
      success: true,
      data: exceptionsData
    });

  } catch (error) {
    console.error('Get exceptions analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve exceptions analytics'
    });
  }
});

// @route   GET /api/analytics/trips
// @desc    Get trip metrics analytics data
// @access  Private
router.get('/trips', auth, async (req, res) => {
  try {
    const { start_date, end_date, truck_id, driver_id } = req.query;

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Date filter
    if (start_date && end_date) {
      paramCount += 2;
      whereConditions.push(`tl.created_at BETWEEN $${paramCount - 1} AND $${paramCount}`);
      queryParams.push(start_date, end_date);
    } else {
      paramCount++;
      whereConditions.push(`tl.created_at >= $${paramCount}`);
      queryParams.push(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
    }

    // Truck filter
    if (truck_id) {
      paramCount++;
      whereConditions.push(`a.truck_id = $${paramCount}`);
      queryParams.push(parseInt(truck_id));
    }

    // Driver filter
    if (driver_id) {
      paramCount++;
      whereConditions.push(`a.driver_id = $${paramCount}`);
      queryParams.push(parseInt(driver_id));
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    const metricsQuery = `
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        AVG(tl.total_duration_minutes) as avg_total_duration,
        AVG(tl.loading_duration_minutes) as avg_loading_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_duration,
        AVG(tl.unloading_duration_minutes) as avg_unloading_duration,
        MIN(tl.total_duration_minutes) as min_duration,
        MAX(tl.total_duration_minutes) as max_duration
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      ${whereClause}
    `;

    const result = await query(metricsQuery, queryParams);
    const metrics = result.rows[0];

    const tripMetrics = {
      totalTrips: parseInt(metrics.total_trips) || 0,
      completedTrips: parseInt(metrics.completed_trips) || 0,
      avgTotalDuration: metrics.avg_total_duration ? Math.round(parseFloat(metrics.avg_total_duration)) : 0,
      avgLoadingDuration: metrics.avg_loading_duration ? Math.round(parseFloat(metrics.avg_loading_duration)) : 0,
      avgTravelDuration: metrics.avg_travel_duration ? Math.round(parseFloat(metrics.avg_travel_duration)) : 0,
      avgUnloadingDuration: metrics.avg_unloading_duration ? Math.round(parseFloat(metrics.avg_unloading_duration)) : 0,
      minDuration: metrics.min_duration ? Math.round(parseFloat(metrics.min_duration)) : 0,
      maxDuration: metrics.max_duration ? Math.round(parseFloat(metrics.max_duration)) : 0,
      completionRate: metrics.total_trips > 0 ? 
        ((parseInt(metrics.completed_trips) / parseInt(metrics.total_trips)) * 100).toFixed(1) : '0.0'
    };

    res.json({
      success: true,
      data: tripMetrics
    });

  } catch (error) {
    console.error('Get trip metrics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trip metrics'
    });
  }
});

// @route   GET /api/analytics/routes
// @desc    Get route performance analytics data
// @access  Private
router.get('/routes', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Route performance analysis
    const routePerformanceQuery = `
      SELECT 
        CONCAT(l1.name, ' → ', l2.name) as route,
        COUNT(tl.id) as trip_count,
        AVG(tl.total_duration_minutes) as avg_duration,
        AVG(tl.travel_duration_minutes) as avg_travel_time,
        (COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as completion_rate,
        (COUNT(CASE WHEN tl.is_exception = false AND tl.status = 'trip_completed' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END), 0)) as efficiency_rate
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN locations l1 ON a.loading_location_id = l1.id
      JOIN locations l2 ON a.unloading_location_id = l2.id
      ${dateFilter}
      GROUP BY l1.name, l2.name, l1.id, l2.id
      HAVING COUNT(tl.id) >= 3
      ORDER BY trip_count DESC, avg_duration ASC
      LIMIT 15
    `;

    const routeResult = await query(routePerformanceQuery, queryParams);

    const routeData = {
      performance: routeResult.rows.map(row => ({
        route: row.route,
        avgTime: row.avg_duration ? Math.round(parseFloat(row.avg_duration)) : 0,
        trips: parseInt(row.trip_count) || 0,
        efficiency: row.efficiency_rate ? Math.round(parseFloat(row.efficiency_rate)) : 0,
        completionRate: row.completion_rate ? parseFloat(row.completion_rate).toFixed(1) : '0.0'
      }))
    };

    res.json({
      success: true,
      data: routeData
    });

  } catch (error) {
    console.error('Get route analytics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve route analytics'
    });
  }
});

// @route   GET /api/analytics/assignment-trends
// @desc    Get assignment creation trends data
// @access  Private
router.get('/assignment-trends', auth, async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    // Convert period to days for SQL query
    let days = 30;
    if (period === '7d') days = 7;
    else if (period === '30d') days = 30;
    else if (period === '90d') days = 90;
    
    // SQL query to get assignment creation trends
    const trendsQuery = `
      SELECT
        DATE_TRUNC('day', created_at)::DATE AS "date",
        COUNT(id) AS "count"
      FROM
        assignments
      WHERE
        created_at >= NOW() - INTERVAL '${days} day'
      GROUP BY
        "date"
      ORDER BY
        "date" ASC;
    `;

    const result = await query(trendsQuery);
    
    // Format the response
    const data = result.rows.map(row => ({
      date: row.date,
      count: parseInt(row.count) || 0
    }));

    res.json({
      success: true,
      data: data
    });

  } catch (error) {
    console.error('Get assignment trends error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve assignment trends'
    });
  }
});

// @route   GET /api/analytics/truck-rankings
// @desc    Get top dump trucks performance rankings
// @access  Private
router.get('/truck-rankings', auth, async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    const queryParams = [];
    
    if (start_date && end_date) {
      dateFilter = 'WHERE tl.created_at BETWEEN $1 AND $2';
      queryParams.push(start_date, end_date);
    } else {
      dateFilter = 'WHERE tl.created_at >= CURRENT_DATE - INTERVAL \'30 days\'';
    }

    // Query for truck performance rankings
    const truckRankingsQuery = `
      SELECT
        dt.truck_number,
        dt.id as truck_id,
        d.full_name as driver_name,
        d.employee_id,
        COUNT(tl.id) as total_completed_trips,
        AVG(tl.total_duration_minutes) as avg_trip_duration_minutes,
        AVG(tl.loading_duration_minutes) as avg_loading_duration_minutes,
        AVG(tl.travel_duration_minutes) as avg_travel_duration_minutes,
        AVG(tl.unloading_duration_minutes) as avg_unloading_duration_minutes,
        MIN(tl.total_duration_minutes) as min_trip_duration,
        MAX(tl.total_duration_minutes) as max_trip_duration,
        COUNT(CASE WHEN tl.is_exception = true THEN 1 END) as exception_count,
        (COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0)) as exception_rate,
        -- Performance score calculation (higher is better)
        ROUND(
          (
            -- Base score from trip completion (40%)
            (COUNT(tl.id) * 0.4) +
            -- Efficiency score based on average duration (30%, inverted so lower duration = higher score)
            (CASE
              WHEN AVG(tl.total_duration_minutes) > 0 THEN
                (300 - LEAST(AVG(tl.total_duration_minutes), 300)) * 0.3 / 300 * 100
              ELSE 0
            END) +
            -- Exception rate score (30%, inverted so lower exception rate = higher score)
            ((100 - LEAST(COUNT(CASE WHEN tl.is_exception = true THEN 1 END) * 100.0 / NULLIF(COUNT(tl.id), 0), 100)) * 0.3)
          ), 2
        ) as performance_score
      FROM dump_trucks dt
      JOIN assignments a ON dt.id = a.truck_id
      JOIN drivers d ON a.driver_id = d.id
      JOIN trip_logs tl ON a.id = tl.assignment_id
      ${dateFilter}
      AND tl.status = 'trip_completed'
      AND dt.status = 'active'
      AND d.status = 'active'
      GROUP BY dt.id, dt.truck_number, d.full_name, d.employee_id
      HAVING COUNT(tl.id) >= 1  -- Only include trucks with at least 1 completed trip
      ORDER BY performance_score DESC, total_completed_trips DESC
      LIMIT 20
    `;

    const result = await query(truckRankingsQuery, queryParams);

    // Format the response
    const truckRankings = result.rows.map((row, index) => ({
      rank: index + 1,
      truckNumber: row.truck_number,
      truckId: parseInt(row.truck_id),
      driverName: row.driver_name,
      employeeId: row.employee_id,
      totalCompletedTrips: parseInt(row.total_completed_trips) || 0,
      avgTripDuration: row.avg_trip_duration_minutes ? Math.round(parseFloat(row.avg_trip_duration_minutes)) : 0,
      avgLoadingDuration: row.avg_loading_duration_minutes ? Math.round(parseFloat(row.avg_loading_duration_minutes)) : 0,
      avgTravelDuration: row.avg_travel_duration_minutes ? Math.round(parseFloat(row.avg_travel_duration_minutes)) : 0,
      avgUnloadingDuration: row.avg_unloading_duration_minutes ? Math.round(parseFloat(row.avg_unloading_duration_minutes)) : 0,
      minTripDuration: row.min_trip_duration ? Math.round(parseFloat(row.min_trip_duration)) : 0,
      maxTripDuration: row.max_trip_duration ? Math.round(parseFloat(row.max_trip_duration)) : 0,
      exceptionCount: parseInt(row.exception_count) || 0,
      exceptionRate: row.exception_rate ? parseFloat(row.exception_rate).toFixed(1) : '0.0',
      performanceScore: parseFloat(row.performance_score) || 0
    }));

    res.json({
      success: true,
      data: {
        rankings: truckRankings,
        totalTrucks: truckRankings.length,
        period: {
          start: start_date || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: end_date || new Date().toISOString().split('T')[0]
        }
      }
    });

  } catch (error) {
    console.error('Get truck rankings error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve truck performance rankings'
    });
  }
});

module.exports = router;