const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function fixDynamicTrip() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing dynamic route discovery trip...');

    // First, let's find the most recent trip
    const recentTrips = await client.query(`
      SELECT
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.loading_start_time, tl.unloading_start_time,
        a.notes as assignment_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 5
    `);

    console.log(`Found ${recentTrips.rows.length} recent trips:`);
    recentTrips.rows.forEach(t => {
      console.log(`  Trip ID ${t.id}: Trip #${t.trip_number}, Status: ${t.status}`);
    });

    if (recentTrips.rows.length === 0) {
      console.log('❌ No recent trips found');
      return;
    }

    // Use the most recent trip
    const currentTrip = { rows: [recentTrips.rows[0]] };

    if (currentTrip.rows.length === 0) {
      console.log('❌ Trip #99 not found');
      return;
    }

    const trip = currentTrip.rows[0];
    console.log('📊 Current trip state:');
    console.log(`  Status: ${trip.status}`);
    console.log(`  Actual loading location ID: ${trip.actual_loading_location_id}`);
    console.log(`  Actual unloading location ID: ${trip.actual_unloading_location_id}`);

    // Check if this is a dynamic assignment
    let isDynamic = false;
    try {
      const notes = JSON.parse(trip.assignment_notes || '{}');
      isDynamic = notes.creation_method === 'dynamic_assignment';
    } catch (e) {
      console.log('❌ Failed to parse assignment notes');
    }

    if (!isDynamic) {
      console.log('❌ This is not a dynamic assignment');
      return;
    }

    console.log('✅ Confirmed: This is a dynamic assignment');

    // The issue: Trip shows loading_start but should be unloading_start at Point C (location ID 3)
    // Fix: Change status to unloading_start and set actual_unloading_location_id to Point C

    await client.query('BEGIN');

    const updateResult = await client.query(`
      UPDATE trip_logs
      SET
        status = 'unloading_start',
        unloading_start_time = COALESCE(loading_start_time, CURRENT_TIMESTAMP),
        loading_start_time = NULL,
        actual_unloading_location_id = 3,
        actual_loading_location_id = NULL,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [trip.id]);

    await client.query('COMMIT');

    console.log('✅ Trip fixed successfully!');
    console.log('📊 New trip state:');
    const fixedTrip = updateResult.rows[0];
    console.log(`  Status: ${fixedTrip.status}`);
    console.log(`  Actual loading location ID: ${fixedTrip.actual_loading_location_id}`);
    console.log(`  Actual unloading location ID: ${fixedTrip.actual_unloading_location_id}`);
    console.log(`  Loading start time: ${fixedTrip.loading_start_time}`);
    console.log(`  Unloading start time: ${fixedTrip.unloading_start_time}`);

    // Verify the fix
    console.log('\n🔍 Verifying fix with location names...');
    const verification = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.id = $1
    `, [trip.id]);

    const verified = verification.rows[0];
    console.log('📋 Final verification:');
    console.log(`  Trip #${verified.trip_number}: ${verified.status}`);
    console.log(`  Assigned route: ${verified.assigned_loading_location} → ${verified.assigned_unloading_location}`);
    console.log(`  Actual route: ${verified.actual_loading_location || 'Not started'} → ${verified.actual_unloading_location || 'Not reached'}`);

    console.log('\n🎉 Dynamic route discovery trip fixed successfully!');
    console.log('✅ Trip now correctly shows unloading_start at Point C - Secondary Dump Site');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error fixing trip:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

fixDynamicTrip().catch(console.error);
