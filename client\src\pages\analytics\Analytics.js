import React, { useState, useEffect, useCallback } from 'react';
import { analyticsAPI } from '../../services/api';
import DashboardStats from './components/DashboardStats';
import PerformanceCharts from './components/PerformanceCharts';
import AssignmentAnalytics from './components/AssignmentAnalytics';
import toast from 'react-hot-toast';

const Analytics = () => {
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [performanceData, setPerformanceData] = useState(null);
  const [assignmentData, setAssignmentData] = useState(null);
  const [truckRankingsData, setTruckRankingsData] = useState(null);

  // Filter states
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  });

  const [activeTab, setActiveTab] = useState('dashboard');

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await analyticsAPI.getDashboard();
      setDashboardData(response.data.data); // FIX: use .data.data for correct structure
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
      // Set fallback data to prevent crashes
      setDashboardData({
        fleet: { totalTrucks: 0, activeTrucks: 0, totalDrivers: 0, activeDrivers: 0, totalLocations: 0, activeLocations: 0 },
        trips: { todayTrips: 0, todayCompleted: 0, weeklyTrips: 0, weeklyCompleted: 0, monthlyTrips: 0, monthlyCompleted: 0 },
        performance: { avgTripTime: 0, completionRate: 0, onTimeRate: 0, exceptionRate: 0 }
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Transform backend data for charts
  const transformPerformanceData = (backendData, routeData = null) => {
    if (!backendData) return null;

    // Transform trends data for trip volume chart
    const tripVolume = {
      labels: backendData.trends?.map(trend => 
        new Date(trend.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      ) || [],
      completed: backendData.trends?.map(trend => trend.completedTrips) || [],
      assigned: backendData.trends?.map(trend => trend.totalTrips) || []
    };

    // Transform for efficiency chart (using average duration data)
    const efficiency = {
      labels: backendData.trends?.slice(-7).map(trend => 
        new Date(trend.date).toLocaleDateString('en-US', { weekday: 'short' })
      ) || ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      avgTripTime: backendData.trends?.slice(-7).map(trend => trend.avgDuration || 0) || [0, 0, 0, 0, 0, 0, 0],
      target: 150 // Default target time
    };

    // Transform truck utilization data
    const trucks = backendData.truckUtilization?.map(truck => ({
      name: truck.truckNumber,
      utilization: parseFloat(truck.completionRate),
      trips: truck.tripCount
    })) || [];

    // Transform driver performance data
    const drivers = backendData.driverPerformance?.map(driver => ({
      name: driver.driverName,
      utilization: parseFloat(driver.completionRate),
      trips: driver.tripCount
    })) || [];

    // Use real route data if available, otherwise empty array
    const routes = routeData ? {
      performance: routeData.performance || []
    } : {
      performance: []
    };

    return {
      tripVolume,
      efficiency,
      utilization: {
        trucks,
        drivers
      },
      routes
    };
  };

  // Load performance data - FIXED API CALL
  const loadPerformanceData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Fix the API calls - pass parameters correctly
      const [performanceResponse, routeResponse] = await Promise.all([
        analyticsAPI.getPerformance({
          start_date: dateRange.start,
          end_date: dateRange.end
        }),
        analyticsAPI.getRoutes({
          start_date: dateRange.start,
          end_date: dateRange.end
        })
      ]);
      
      const transformedData = transformPerformanceData(
        performanceResponse.data.data, // Fixed: access .data.data from backend response
        routeResponse.data.data
      );
      setPerformanceData(transformedData);
    } catch (error) {
      console.error('Error loading performance data:', error);
      toast.error('Failed to load performance data');
      // Set fallback data to prevent crashes
      setPerformanceData({
        tripVolume: { labels: [], completed: [], assigned: [] },
        efficiency: { labels: [], avgTripTime: [], target: 150 },
        utilization: { trucks: [], drivers: [] },
        routes: { performance: [] }
      });
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Load assignment data - NEW API CALL
  const loadAssignmentData = useCallback(async () => {
    try {
      setLoading(true);

      const response = await analyticsAPI.getAssignments({
        start_date: dateRange.start,
        end_date: dateRange.end
      });

      setAssignmentData(response.data.data); // Access .data.data from backend response
    } catch (error) {
      console.error('Error loading assignment data:', error);
      toast.error('Failed to load assignment data');
      // Set fallback data to prevent crashes
      setAssignmentData({
        summary: { total: 0, active: 0, completed: 0, autoCreated: 0, completionRate: '0.0' },
        recent: [],
        trends: [],
        performance: { avgCreationTime: 0, successRate: 0, autoAssignmentRate: 0 }
      });
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Load truck rankings data - NEW API CALL
  const loadTruckRankingsData = useCallback(async () => {
    try {
      setLoading(true);

      const response = await analyticsAPI.getTruckRankings({
        start_date: dateRange.start,
        end_date: dateRange.end
      });

      setTruckRankingsData(response.data.data); // Access .data.data from backend response
    } catch (error) {
      console.error('Error loading truck rankings data:', error);
      toast.error('Failed to load truck rankings data');
      // Set fallback data to prevent crashes
      setTruckRankingsData({
        rankings: [],
        totalTrucks: 0,
        period: { start: dateRange.start, end: dateRange.end }
      });
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Initial load
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'performance') {
      loadPerformanceData();
      loadTruckRankingsData();
    } else if (activeTab === 'assignments') {
      loadAssignmentData();
    }
  }, [activeTab, loadPerformanceData, loadAssignmentData, loadTruckRankingsData]);

  // Handle date range change
  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    if (activeTab === 'dashboard') {
      loadDashboardData();
    } else if (activeTab === 'performance') {
      loadPerformanceData();
      loadTruckRankingsData();
    } else if (activeTab === 'assignments') {
      loadAssignmentData();
    }
    toast.success('Data refreshed');
  };

  // Handle data export with comprehensive data aggregation
  const handleExportData = async (data, format) => {
    try {
      // Aggregate all available data for export
      const exportData = await aggregateExportData(data);
      
      if (format === 'csv') {
        exportToCSV(exportData);
      } else if (format === 'pdf') {
        exportToPDF(exportData);
      }
      toast.success(`Analytics data exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export data');
    }
  };

  // Aggregate all available data for comprehensive export
  const aggregateExportData = async (primaryData) => {
    try {
      // Get comprehensive data from all sources
      const [dashboardResponse, performanceResponse, assignmentResponse, truckRankingsResponse] = await Promise.all([
        dashboardData ? Promise.resolve({ data: { data: dashboardData } }) : analyticsAPI.getDashboard(),
        performanceData ? Promise.resolve({ data: { data: performanceData } }) : analyticsAPI.getPerformance({
          start_date: dateRange.start,
          end_date: dateRange.end
        }),
        assignmentData ? Promise.resolve({ data: { data: assignmentData } }) : analyticsAPI.getAssignments({
          start_date: dateRange.start,
          end_date: dateRange.end
        }),
        truckRankingsData ? Promise.resolve({ data: { data: truckRankingsData } }) : analyticsAPI.getTruckRankings({
          start_date: dateRange.start,
          end_date: dateRange.end
        })
      ]);

      return {
        dashboard: dashboardResponse.data?.data || dashboardData,
        performance: performanceResponse.data?.data || performanceData,
        assignments: assignmentResponse.data?.data || assignmentData,
        truckRankings: truckRankingsResponse.data?.data || truckRankingsData,
        dateRange: dateRange,
        exportedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error aggregating export data:', error);
      // Fallback to available data
      return {
        dashboard: dashboardData,
        performance: performanceData,
        assignments: assignmentData,
        truckRankings: truckRankingsData,
        dateRange: dateRange,
        exportedAt: new Date().toISOString()
      };
    }
  };

  // Enhanced CSV export with comprehensive analytics data
  const exportToCSV = (data) => {
    let csvContent = '';
    
    // Header information
    csvContent += `Hauling QR Trip System - Analytics Report\n`;
    csvContent += `Generated on: ${new Date().toLocaleDateString()}\n`;
    csvContent += `Period: ${formatDateRange()}\n`;
    csvContent += `Developer: Ariez-AI\n\n`;

    // Dashboard Summary
    if (data.dashboard) {
      csvContent += 'DASHBOARD SUMMARY\n';
      csvContent += 'Metric,Value\n';
      csvContent += `Total Trucks,${data.dashboard.fleet?.totalTrucks || 0}\n`;
      csvContent += `Active Trucks,${data.dashboard.fleet?.activeTrucks || 0}\n`;
      csvContent += `Total Drivers,${data.dashboard.fleet?.totalDrivers || 0}\n`;
      csvContent += `Active Drivers,${data.dashboard.fleet?.activeDrivers || 0}\n`;
      csvContent += `Total Locations,${data.dashboard.fleet?.totalLocations || 0}\n`;
      csvContent += `Active Locations,${data.dashboard.fleet?.activeLocations || 0}\n`;
      csvContent += `Today Trips,${data.dashboard.trips?.todayTrips || 0}\n`;
      csvContent += `Today Completed,${data.dashboard.trips?.todayCompleted || 0}\n`;
      csvContent += `Weekly Trips,${data.dashboard.trips?.weeklyTrips || 0}\n`;
      csvContent += `Monthly Trips,${data.dashboard.trips?.monthlyTrips || 0}\n`;
      csvContent += `Average Trip Time (min),${data.dashboard.performance?.avgTripTime || 0}\n`;
      csvContent += `Completion Rate %,${data.dashboard.performance?.completionRate || 0}\n`;
      csvContent += `Exception Rate %,${data.dashboard.performance?.exceptionRate || 0}\n\n`;
    }

    // Assignment Analytics
    if (data.assignments?.summary) {
      csvContent += 'ASSIGNMENT ANALYTICS\n';
      csvContent += 'Metric,Value\n';
      csvContent += `Total Assignments,${data.assignments.summary.total || 0}\n`;
      csvContent += `Active Assignments,${data.assignments.summary.active || 0}\n`;
      csvContent += `Completed Assignments,${data.assignments.summary.completed || 0}\n`;
      csvContent += `Auto-Created Assignments,${data.assignments.summary.autoCreated || 0}\n`;
      csvContent += `Assignment Completion Rate %,${data.assignments.summary.completionRate || 0}\n`;
      csvContent += `Auto-Assignment Rate %,${data.assignments.performance?.autoAssignmentRate || 0}\n`;
      csvContent += `Average Creation Time (ms),${data.assignments.performance?.avgCreationTime || 0}\n\n`;

      // Recent Assignments
      if (data.assignments.recent && data.assignments.recent.length > 0) {
        csvContent += 'RECENT ASSIGNMENTS\n';
        csvContent += 'Assignment Code,Truck,Loading Location,Unloading Location,Status,Type,Created\n';
        data.assignments.recent.forEach(assignment => {
          csvContent += `${assignment.assignment_code || ''},${assignment.truck_number || ''},${assignment.loading_location || ''},${assignment.unloading_location || ''},${assignment.status || ''},${assignment.is_auto_created ? 'Auto' : 'Manual'},${assignment.created_at ? new Date(assignment.created_at).toLocaleDateString() : ''}\n`;
        });
        csvContent += '\n';
      }
    }

    // Performance Data
    if (data.performance) {
      // Trip Volume Data
      if (data.performance.tripVolume && data.performance.tripVolume.labels?.length > 0) {
        csvContent += 'TRIP VOLUME TRENDS\n';
        csvContent += 'Period,Completed Trips,Total Trips\n';
        data.performance.tripVolume.labels.forEach((label, index) => {
          csvContent += `${label},${data.performance.tripVolume.completed[index] || 0},${data.performance.tripVolume.assigned[index] || 0}\n`;
        });
        csvContent += '\n';
      }

      // Truck Utilization
      if (data.performance.utilization?.trucks && data.performance.utilization.trucks.length > 0) {
        csvContent += 'TRUCK UTILIZATION\n';
        csvContent += 'Truck,Utilization %,Trip Count\n';
        data.performance.utilization.trucks.forEach(truck => {
          csvContent += `${truck.name},${truck.utilization},${truck.trips}\n`;
        });
        csvContent += '\n';
      }

      // Driver Performance
      if (data.performance.utilization?.drivers && data.performance.utilization.drivers.length > 0) {
        csvContent += 'DRIVER PERFORMANCE\n';
        csvContent += 'Driver,Utilization %,Trip Count\n';
        data.performance.utilization.drivers.forEach(driver => {
          csvContent += `${driver.name},${driver.utilization},${driver.trips}\n`;
        });
        csvContent += '\n';
      }

      // Route Performance
      if (data.performance.routes?.performance && data.performance.routes.performance.length > 0) {
        csvContent += 'ROUTE PERFORMANCE\n';
        csvContent += 'Route,Average Time (min),Trip Count,Efficiency %\n';
        data.performance.routes.performance.forEach(route => {
          csvContent += `${route.route},${route.avgTime},${route.trips},${route.efficiency}\n`;
        });
        csvContent += '\n';
      }
    }

    // Truck Performance Rankings
    if (data.truckRankings?.rankings && data.truckRankings.rankings.length > 0) {
      csvContent += 'TRUCK PERFORMANCE RANKINGS\n';
      csvContent += 'Rank,Truck Number,Driver Name,Employee ID,Total Trips,Avg Duration (min),Exception Rate %,Performance Score\n';
      data.truckRankings.rankings.forEach(truck => {
        csvContent += `${truck.rank},${truck.truckNumber},"${truck.driverName}",${truck.employeeId},${truck.totalCompletedTrips},${truck.avgTripDuration},${truck.exceptionRate},${truck.performanceScore}\n`;
      });
      csvContent += '\n';
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `hauling-analytics-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Enhanced PDF export with comprehensive analytics
  const exportToPDF = (data) => {
    const printWindow = window.open('', '_blank');
    let htmlContent = `
      <html>
        <head>
          <title>Hauling QR Analytics Report - ${new Date().toLocaleDateString()}</title>
          <style>
            body { font-family: 'Segoe UI', Arial, sans-serif; margin: 20px; color: #333; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #4caf50; padding-bottom: 20px; }
            .header h1 { color: #4caf50; margin: 0; font-size: 28px; }
            .header p { margin: 5px 0; color: #666; }
            .section { margin-bottom: 30px; page-break-inside: avoid; }
            .section h2 { color: #4caf50; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
            th { background-color: #f8f9fa; font-weight: bold; color: #333; }
            .summary-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 20px; }
            .summary-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
            .summary-box h3 { margin: 0 0 10px 0; color: #4caf50; font-size: 16px; }
            .metric { display: flex; justify-content: space-between; margin: 5px 0; }
            .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #666; }
            @media print { body { margin: 10px; } .section { page-break-inside: avoid; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>🚛 Hauling QR Trip System</h1>
            <h2>Analytics & Performance Report</h2>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            <p><strong>Period:</strong> ${formatDateRange()}</p>
            <p><strong>Developer:</strong> Ariez-AI</p>
          </div>
    `;

    // Dashboard Summary Section
    if (data.dashboard) {
      htmlContent += `
        <div class="section">
          <h2>📊 System Overview</h2>
          <div class="summary-grid">
            <div class="summary-box">
              <h3>Fleet Status</h3>
              <div class="metric"><span>Total Trucks:</span><span>${data.dashboard.fleet?.totalTrucks || 0}</span></div>
              <div class="metric"><span>Active Trucks:</span><span>${data.dashboard.fleet?.activeTrucks || 0}</span></div>
              <div class="metric"><span>Total Drivers:</span><span>${data.dashboard.fleet?.totalDrivers || 0}</span></div>
              <div class="metric"><span>Active Drivers:</span><span>${data.dashboard.fleet?.activeDrivers || 0}</span></div>
              <div class="metric"><span>Total Locations:</span><span>${data.dashboard.fleet?.totalLocations || 0}</span></div>
            </div>
            <div class="summary-box">
              <h3>Performance Metrics</h3>
              <div class="metric"><span>Today's Trips:</span><span>${data.dashboard.trips?.todayTrips || 0}</span></div>
              <div class="metric"><span>Today Completed:</span><span>${data.dashboard.trips?.todayCompleted || 0}</span></div>
              <div class="metric"><span>Avg Trip Time:</span><span>${data.dashboard.performance?.avgTripTime || 0} min</span></div>
              <div class="metric"><span>Completion Rate:</span><span>${data.dashboard.performance?.completionRate || 0}%</span></div>
              <div class="metric"><span>Exception Rate:</span><span>${data.dashboard.performance?.exceptionRate || 0}%</span></div>
            </div>
          </div>
        </div>
      `;
    }

    // Assignment Analytics Section
    if (data.assignments?.summary) {
      htmlContent += `
        <div class="section">
          <h2>📋 Assignment Analytics</h2>
          <table>
            <thead>
              <tr><th>Metric</th><th>Value</th><th>Description</th></tr>
            </thead>
            <tbody>
              <tr><td>Total Assignments</td><td>${data.assignments.summary.total || 0}</td><td>All assignments created</td></tr>
              <tr><td>Active Assignments</td><td>${data.assignments.summary.active || 0}</td><td>Currently in progress</td></tr>
              <tr><td>Auto-Created</td><td>${data.assignments.summary.autoCreated || 0}</td><td>Created by AutoAssignmentCreator</td></tr>
              <tr><td>Completion Rate</td><td>${data.assignments.summary.completionRate || 0}%</td><td>Successfully completed assignments</td></tr>
              <tr><td>Auto-Assignment Rate</td><td>${data.assignments.performance?.autoAssignmentRate || 0}%</td><td>Percentage created automatically</td></tr>
            </tbody>
          </table>
        </div>
      `;
    }

    // Truck Utilization Section
    if (data.performance?.utilization?.trucks && data.performance.utilization.trucks.length > 0) {
      htmlContent += `
        <div class="section">
          <h2>🚛 Truck Utilization</h2>
          <table>
            <thead>
              <tr><th>Truck Number</th><th>Utilization %</th><th>Trip Count</th><th>Performance</th></tr>
            </thead>
            <tbody>
      `;
      data.performance.utilization.trucks.forEach(truck => {
        const performance = truck.utilization >= 80 ? 'Excellent' : truck.utilization >= 60 ? 'Good' : truck.utilization >= 40 ? 'Average' : 'Below Average';
        htmlContent += `<tr><td>${truck.name}</td><td>${truck.utilization}%</td><td>${truck.trips}</td><td>${performance}</td></tr>`;
      });
      htmlContent += '</tbody></table></div>';
    }

    // Driver Performance Section
    if (data.performance?.utilization?.drivers && data.performance.utilization.drivers.length > 0) {
      htmlContent += `
        <div class="section">
          <h2>👤 Driver Performance</h2>
          <table>
            <thead>
              <tr><th>Driver Name</th><th>Utilization %</th><th>Trip Count</th><th>Performance</th></tr>
            </thead>
            <tbody>
      `;
      data.performance.utilization.drivers.forEach(driver => {
        const performance = driver.utilization >= 80 ? 'Excellent' : driver.utilization >= 60 ? 'Good' : driver.utilization >= 40 ? 'Average' : 'Below Average';
        htmlContent += `<tr><td>${driver.name}</td><td>${driver.utilization}%</td><td>${driver.trips}</td><td>${performance}</td></tr>`;
      });
      htmlContent += '</tbody></table></div>';
    }

    // Route Performance Section
    if (data.performance?.routes?.performance && data.performance.routes.performance.length > 0) {
      htmlContent += `
        <div class="section">
          <h2>🛣️ Route Performance</h2>
          <table>
            <thead>
              <tr><th>Route</th><th>Avg Time (min)</th><th>Trip Count</th><th>Efficiency %</th></tr>
            </thead>
            <tbody>
      `;
      data.performance.routes.performance.forEach(route => {
        htmlContent += `<tr><td>${route.route}</td><td>${route.avgTime}</td><td>${route.trips}</td><td>${route.efficiency}%</td></tr>`;
      });
      htmlContent += '</tbody></table></div>';
    }

    // Truck Performance Rankings Section
    if (data.truckRankings?.rankings && data.truckRankings.rankings.length > 0) {
      htmlContent += `
        <div class="section">
          <h2>🏆 Top Dump Trucks Performance Rankings</h2>
          <table>
            <thead>
              <tr><th>Rank</th><th>Truck</th><th>Driver</th><th>Total Trips</th><th>Avg Duration</th><th>Exception Rate</th><th>Performance Score</th></tr>
            </thead>
            <tbody>
      `;
      data.truckRankings.rankings.slice(0, 10).forEach(truck => {
        const rankIcon = truck.rank === 1 ? '🥇' : truck.rank === 2 ? '🥈' : truck.rank === 3 ? '🥉' : `#${truck.rank}`;
        const performanceClass = truck.performanceScore >= 80 ? 'Excellent' : truck.performanceScore >= 60 ? 'Good' : truck.performanceScore >= 40 ? 'Average' : 'Below Average';
        htmlContent += `<tr>
          <td>${rankIcon}</td>
          <td>${truck.truckNumber}</td>
          <td>${truck.driverName} (${truck.employeeId})</td>
          <td>${truck.totalCompletedTrips}</td>
          <td>${Math.floor(truck.avgTripDuration / 60)}h ${truck.avgTripDuration % 60}m</td>
          <td>${truck.exceptionRate}%</td>
          <td>${truck.performanceScore.toFixed(1)} (${performanceClass})</td>
        </tr>`;
      });
      htmlContent += '</tbody></table></div>';
    }

    htmlContent += `
          <div class="footer">
            <p><strong>© 2025 Hauling QR Trip System</strong> • Developer: Ariez-AI</p>
            <p>This report contains comprehensive analytics data for operational insights and performance monitoring.</p>
          </div>
        </body>
      </html>
    `;
    
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  const formatDateRange = () => {
    const start = new Date(dateRange.start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const end = new Date(dateRange.end).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    return `${start} - ${end}`;
  };

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'performance', name: 'Performance', icon: '📈' },
    { id: 'assignments', name: 'Assignments', icon: '📋' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Analytics & Reports</h1>
          <p className="text-secondary-600 mt-1">Performance metrics and operational insights</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Date Range Selector */}
          {activeTab !== 'dashboard' && (
            <div className="flex items-center space-x-2">
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className="text-sm border-secondary-300 rounded-md"
              />
              <span className="text-secondary-500">to</span>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className="text-sm border-secondary-300 rounded-md"
              />
            </div>
          )}
          
          <button
            onClick={handleRefresh}
            className="btn btn-secondary"
            disabled={loading}
          >
            <svg className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {loading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white shadow-sm border border-secondary-200 rounded-lg">
        <div className="border-b border-secondary-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <span className="text-lg">{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'dashboard' && (
            <DashboardStats 
              data={dashboardData} 
              loading={loading} 
            />
          )}

          {activeTab === 'performance' && (
            <PerformanceCharts
              data={performanceData}
              truckRankingsData={truckRankingsData}
              loading={loading}
              dateRange={dateRange}
              onExportData={handleExportData}
            />
          )}

          {activeTab === 'assignments' && (
            <AssignmentAnalytics
              data={assignmentData}
              loading={loading}
              dateRange={dateRange}
            />
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            className="flex items-center p-4 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors"
            onClick={() => handleExportData(performanceData || dashboardData, 'csv')}
          >
            <span className="text-2xl mr-3">📄</span>
            <div className="text-left">
              <h4 className="font-medium text-secondary-900">Export Report (CSV)</h4>
              <p className="text-sm text-secondary-500">Download analytics data as CSV</p>
            </div>
          </button>

          <button 
            className="flex items-center p-4 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors"
            onClick={() => handleExportData(performanceData || dashboardData, 'pdf')}
          >
            <span className="text-2xl mr-3">📄</span>
            <div className="text-left">
              <h4 className="font-medium text-secondary-900">Export Report (PDF)</h4>
              <p className="text-sm text-secondary-500">Download analytics data as PDF</p>
            </div>
          </button>

          <button className="flex items-center p-4 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors">
            <span className="text-2xl mr-3">🔔</span>
            <div className="text-left">
              <h4 className="font-medium text-secondary-900">Alert Settings</h4>
              <p className="text-sm text-secondary-500">Configure performance alerts</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Analytics;