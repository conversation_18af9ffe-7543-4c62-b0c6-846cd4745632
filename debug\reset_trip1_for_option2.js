const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function resetTrip1ForOption2() {
  const client = await pool.connect();
  try {
    console.log('🔄 Resetting Trip #1 to proper Option 2 state...');

    // Get current Trip #1 state
    const trip1Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.unloading_start_time, tl.unloading_end_time, tl.trip_completed_time,
        tl.total_duration_minutes, tl.unloading_duration_minutes,
        a.notes as assignment_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.id = 105
    `);

    if (trip1Result.rows.length === 0) {
      console.log('❌ Trip #1 (ID: 105) not found');
      return;
    }

    const trip1 = trip1Result.rows[0];
    console.log(`📊 Current Trip #1 State:`);
    console.log(`   Status: ${trip1.status}`);
    console.log(`   Unloading Start: ${trip1.unloading_start_time}`);
    console.log(`   Unloading End: ${trip1.unloading_end_time || 'NULL'}`);
    console.log(`   Trip Completed: ${trip1.trip_completed_time || 'NULL'}`);

    // Check if this is a dynamic assignment
    let isDynamicAssignment = false;
    if (trip1.assignment_notes) {
      try {
        const assignmentNotes = JSON.parse(trip1.assignment_notes);
        isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
      } catch (e) {
        console.log('❌ Failed to parse assignment notes');
      }
    }

    if (!isDynamicAssignment) {
      console.log('❌ Trip #1 is not a dynamic assignment');
      return;
    }

    console.log('✅ Trip #1 is a dynamic assignment');

    if (trip1.status === 'unloading_start') {
      console.log('✅ Trip #1 is already in the correct state for Option 2');
      return;
    }

    // Reset Trip #1 to unloading_start state for Option 2
    console.log('\n🔄 Resetting Trip #1 to unloading_start state...');

    await client.query('BEGIN');

    try {
      // Reset the trip to unloading_start state
      const resetResult = await client.query(`
        UPDATE trip_logs 
        SET 
          status = 'unloading_start',
          unloading_end_time = NULL,
          trip_completed_time = NULL,
          total_duration_minutes = NULL,
          unloading_duration_minutes = NULL,
          updated_at = CURRENT_TIMESTAMP,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb
        WHERE id = $2
        RETURNING *
      `, [
        {
          reset_reason: 'Reset for Option 2 implementation testing',
          reset_timestamp: new Date().toISOString(),
          previous_status: trip1.status
        },
        trip1.id
      ]);

      await client.query('COMMIT');

      const resetTrip = resetResult.rows[0];
      console.log('✅ Trip #1 reset successfully!');
      console.log(`   New Status: ${resetTrip.status}`);
      console.log(`   Unloading Start: ${resetTrip.unloading_start_time}`);
      console.log(`   Unloading End: ${resetTrip.unloading_end_time || 'NULL'}`);
      console.log(`   Trip Completed: ${resetTrip.trip_completed_time || 'NULL'}`);

      console.log('\n🎯 Option 2 State Achieved:');
      console.log('   ✅ Trip #2: Auto-completed (preserves audit trail)');
      console.log('   ✅ Trip #1: Active with unloading_start status');
      console.log('   ✅ Trip #1: Dynamic assignment with Point A → Point C route');
      console.log('   ✅ Trip #1: Ready to continue unloading workflow');

      // Verify the final state
      console.log('\n📋 Final Verification:');
      const verifyResult = await client.query(`
        SELECT 
          tl.id, tl.trip_number, tl.status,
          tl.unloading_start_time, tl.unloading_end_time, tl.trip_completed_time,
          ll.name as assigned_loading_location,
          ul.name as assigned_unloading_location,
          aul_loc.name as actual_unloading_location
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
        WHERE tl.id = $1
      `, [trip1.id]);

      const verifiedTrip = verifyResult.rows[0];
      console.log(`   Trip #${verifiedTrip.trip_number}: ${verifiedTrip.status}`);
      console.log(`   Route: ${verifiedTrip.assigned_loading_location} → ${verifiedTrip.assigned_unloading_location}`);
      console.log(`   Actual Unloading: ${verifiedTrip.actual_unloading_location}`);
      console.log(`   Next Action: Scan at Point C to complete unloading`);

      console.log('\n🎉 Option 2 Implementation Ready!');
      console.log('✅ Trip #1 can now progress through:');
      console.log('   1. unloading_start → unloading_end (scan at Point C)');
      console.log('   2. unloading_end → trip_completed (scan at Point A)');

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('❌ Error resetting Trip #1:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

resetTrip1ForOption2().catch(console.error);
