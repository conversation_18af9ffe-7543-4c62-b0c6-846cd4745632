const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeTripStates() {
  const client = await pool.connect();
  try {
    console.log('🔍 Analyzing Current Trip States for Dynamic Route Discovery...');

    // Get all recent trips with detailed information
    const tripsResult = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time, tl.total_duration_minutes,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.created_at, tl.updated_at, tl.notes as trip_notes,
        a.assignment_code, a.notes as assignment_notes,
        a.loading_location_id as assigned_loading_id,
        a.unloading_location_id as assigned_unloading_id,
        dt.truck_number,
        d.full_name as driver_name,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.trip_number DESC, tl.created_at DESC
      LIMIT 10
    `);

    console.log(`\n📊 Found ${tripsResult.rows.length} recent trips:`);
    console.log('=' .repeat(80));

    tripsResult.rows.forEach((trip, index) => {
      console.log(`\n🚚 Trip #${trip.trip_number} (ID: ${trip.id}) - ${trip.truck_number}`);
      console.log(`   Status: ${trip.status}`);
      console.log(`   Assignment: ${trip.assignment_code}`);
      console.log(`   Driver: ${trip.driver_name}`);
      console.log(`   Created: ${trip.created_at}`);
      console.log(`   Updated: ${trip.updated_at}`);
      
      // Route information
      console.log(`\n   📍 Route Information:`);
      console.log(`      Assigned: ${trip.assigned_loading_location} → ${trip.assigned_unloading_location}`);
      console.log(`      Actual: ${trip.actual_loading_location || 'None'} → ${trip.actual_unloading_location || 'None'}`);
      
      // Timestamps
      console.log(`\n   ⏰ Timestamps:`);
      console.log(`      Loading Start: ${trip.loading_start_time || 'NULL'}`);
      console.log(`      Loading End: ${trip.loading_end_time || 'NULL'}`);
      console.log(`      Unloading Start: ${trip.unloading_start_time || 'NULL'}`);
      console.log(`      Unloading End: ${trip.unloading_end_time || 'NULL'}`);
      console.log(`      Trip Completed: ${trip.trip_completed_time || 'NULL'}`);
      console.log(`      Duration: ${trip.total_duration_minutes || 'NULL'} minutes`);
      
      // Assignment analysis
      console.log(`\n   📝 Assignment Analysis:`);
      if (trip.assignment_notes) {
        try {
          const assignmentNotes = JSON.parse(trip.assignment_notes);
          console.log(`      Creation Method: ${assignmentNotes.creation_method || 'Unknown'}`);
          if (assignmentNotes.route_discovery) {
            console.log(`      Route Discovery Mode: ${assignmentNotes.route_discovery.mode || 'Unknown'}`);
            console.log(`      Discovery Type: ${assignmentNotes.route_discovery.discovery_type || 'Unknown'}`);
          }
          if (assignmentNotes.auto_created) {
            console.log(`      Auto Created: Yes`);
          }
        } catch (e) {
          console.log(`      Notes: ${trip.assignment_notes.substring(0, 100)}...`);
        }
      } else {
        console.log(`      Notes: None`);
      }
      
      // Trip notes
      if (trip.trip_notes) {
        try {
          const tripNotes = JSON.parse(trip.trip_notes);
          console.log(`\n   📋 Trip Notes:`);
          if (tripNotes.completion_location_name) {
            console.log(`      Completion Location: ${tripNotes.completion_location_name}`);
          }
          if (tripNotes.completion_method) {
            console.log(`      Completion Method: ${tripNotes.completion_method}`);
          }
        } catch (e) {
          const notesStr = typeof trip.trip_notes === 'string' ? trip.trip_notes : JSON.stringify(trip.trip_notes);
          console.log(`\n   📋 Trip Notes: ${notesStr.substring(0, 100)}...`);
        }
      }
      
      console.log('   ' + '-'.repeat(70));
    });

    // Analyze the specific scenario
    console.log('\n🔍 Scenario Analysis:');
    console.log('=' .repeat(50));
    
    const trip1 = tripsResult.rows.find(t => t.trip_number === 1);
    const trip2 = tripsResult.rows.find(t => t.trip_number === 2);
    
    if (trip1 && trip2) {
      console.log(`\n📊 Trip #2 (ID: ${trip2.id}):`);
      console.log(`   Status: ${trip2.status}`);
      console.log(`   Route: ${trip2.assigned_loading_location} → ${trip2.assigned_unloading_location}`);
      console.log(`   Actual: ${trip2.actual_loading_location || 'None'} → ${trip2.actual_unloading_location || 'None'}`);
      
      console.log(`\n📊 Trip #1 (ID: ${trip1.id}):`);
      console.log(`   Status: ${trip1.status}`);
      console.log(`   Route: ${trip1.assigned_loading_location} → ${trip1.assigned_unloading_location}`);
      console.log(`   Actual: ${trip1.actual_loading_location || 'None'} → ${trip1.actual_unloading_location || 'None'}`);
      
      // Check if Trip #1 is properly configured for dynamic route discovery
      let isDynamicAssignment = false;
      if (trip1.assignment_notes) {
        try {
          const notes = JSON.parse(trip1.assignment_notes);
          isDynamicAssignment = notes.creation_method === 'dynamic_assignment';
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      console.log(`\n✅ Option 2 Implementation Status:`);
      console.log(`   Trip #2 Status: ${trip2.status === 'trip_completed' ? '✅ Completed (as expected)' : '❌ Not completed'}`);
      console.log(`   Trip #1 Dynamic Assignment: ${isDynamicAssignment ? '✅ Yes' : '❌ No'}`);
      console.log(`   Trip #1 Route Reflects Point C: ${trip1.assigned_unloading_location === 'Point C - Secondary Dump Site' ? '✅ Yes' : '❌ No'}`);
      console.log(`   Trip #1 Status: ${trip1.status === 'unloading_start' ? '✅ Unloading Start (correct)' : `❌ ${trip1.status} (unexpected)`}`);
      
    } else {
      console.log('❌ Could not find both Trip #1 and Trip #2');
    }

  } catch (error) {
    console.error('❌ Error analyzing trip states:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeTripStates().catch(console.error);
