# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=your_password_here

# Database Pool Configuration
DB_POOL_MAX=25
DB_POOL_MIN=5

# Application Configuration
NODE_ENV=development
PORT=5000

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/combined.log
ERROR_LOG_FILE=logs/error.log

# WebSocket Configuration
WS_PORT=3001

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Security Configuration
CORS_ORIGIN=http://localhost:3000
HELMET_ENABLED=true

# Performance Configuration
QUERY_TIMEOUT=25000
STATEMENT_TIMEOUT=30000

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=300000
PERFORMANCE_MONITORING=true
