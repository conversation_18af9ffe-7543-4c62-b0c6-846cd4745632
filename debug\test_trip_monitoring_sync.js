#!/usr/bin/env node

/**
 * Test Trip Monitoring Sync with Dynamic Route Discovery
 * 
 * This script tests that the Trip Monitoring data table properly syncs with
 * the dynamic route discovery system and displays uncertainty indicators correctly.
 */

const { getClient } = require('../server/config/database');
const { AutoAssignmentCreator } = require('../server/utils/AutoAssignmentCreator');

async function testTripMonitoringSync() {
  const client = await getClient();
  
  try {
    console.log('🔧 Testing Trip Monitoring Sync with Dynamic Route Discovery...\n');

    // Test data
    const testTruck = {
      id: 1,
      truck_number: 'DT-100',
      status: 'active'
    };

    const testLocations = [
      { id: 1, name: 'Point A - Main Loading Site', type: 'loading' },
      { id: 2, name: 'Point B - Primary Dump Site', type: 'unloading' },
      { id: 3, name: 'Point C - Secondary Dump Site', type: 'unloading' }
    ];

    const userId = 1;

    // Clean up and prepare test environment
    console.log('🧹 Preparing test environment...');
    await client.query(`DELETE FROM trip_logs WHERE assignment_id IN (SELECT id FROM assignments WHERE truck_id = $1)`, [testTruck.id]);
    await client.query(`DELETE FROM assignments WHERE truck_id = $1`, [testTruck.id]);
    
    // Create historical assignment for AutoAssignmentCreator
    await client.query(`
      INSERT INTO assignments (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, assigned_date, status, priority, expected_loads_per_day, notes, created_at, updated_at)
      VALUES ('HIST-SYNC-001', $1, 1, 1, 2, CURRENT_DATE, 'completed', 'high', 1, '{"creation_method": "test_historical"}', NOW(), NOW())
    `, [testTruck.id]);

    console.log('✅ Test environment prepared\n');

    // Test 1: Create dynamic assignment and trip
    console.log('📊 Test 1: Dynamic Assignment and Trip Creation');
    console.log('=' .repeat(60));

    const autoAssignmentCreator = new AutoAssignmentCreator();
    
    // Create dynamic assignment
    const dynamicAssignment = await autoAssignmentCreator.createAutoAssignment({
      truck: testTruck,
      location: testLocations[0], // Loading location
      client,
      userId,
      enableDynamicRouting: true
    });

    console.log(`✅ Dynamic assignment created: ${dynamicAssignment.assignment_code}`);

    // Create a trip for this assignment
    const tripResult = await client.query(`
      INSERT INTO trip_logs (assignment_id, trip_number, status, created_at, updated_at)
      VALUES ($1, 1, 'loading_start', NOW(), NOW())
      RETURNING *
    `, [dynamicAssignment.id]);

    const trip = tripResult.rows[0];
    console.log(`✅ Trip created: Trip #${trip.trip_number} with status ${trip.status}`);

    // Test 2: Query trips data as the frontend would
    console.log('\n📊 Test 2: Frontend Trips Data Query');
    console.log('=' .repeat(60));

    const tripsQuery = `
      SELECT 
        t.id, t.trip_number, t.status, t.loading_start_time, t.loading_end_time,
        t.unloading_start_time, t.unloading_end_time, t.trip_completed_time,
        t.is_exception, t.exception_reason, t.total_duration_minutes,
        t.loading_duration_minutes, t.travel_duration_minutes, t.unloading_duration_minutes,
        t.notes, t.created_at, t.updated_at,
        a.id as assignment_id, a.assigned_date, a.notes as assignment_notes,
        tr.id as truck_id, tr.truck_number, tr.license_plate,
        d.id as driver_id, d.employee_id, d.full_name as driver_name,
        ll.id as loading_location_id, ll.name as loading_location,
        ul.id as unloading_location_id, ul.name as unloading_location
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks tr ON a.truck_id = tr.id
      JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON COALESCE(t.actual_loading_location_id, a.loading_location_id) = ll.id
      LEFT JOIN locations ul ON COALESCE(t.actual_unloading_location_id, a.unloading_location_id) = ul.id
      WHERE t.id = $1
    `;

    const tripDataResult = await client.query(tripsQuery, [trip.id]);
    const tripData = tripDataResult.rows[0];

    console.log('📋 Trip Data Retrieved:');
    console.log(`   Trip #${tripData.trip_number}: ${tripData.status}`);
    console.log(`   Truck: ${tripData.truck_number}`);
    console.log(`   Driver: ${tripData.driver_name}`);
    console.log(`   Loading Location: ${tripData.loading_location}`);
    console.log(`   Unloading Location: ${tripData.unloading_location}`);
    console.log(`   Assignment Notes: ${tripData.assignment_notes ? 'Present' : 'Missing'}`);

    // Test 3: Dynamic Route Detection Logic
    console.log('\n📊 Test 3: Dynamic Route Detection Logic');
    console.log('=' .repeat(60));

    // Parse assignment notes to check for dynamic assignment
    let isDynamicAssignment = false;
    let routeDiscovery = null;

    try {
      const assignmentNotes = JSON.parse(tripData.assignment_notes || '{}');
      isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
      routeDiscovery = assignmentNotes.route_discovery;
      
      console.log(`✅ Assignment notes parsed successfully`);
      console.log(`   Creation method: ${assignmentNotes.creation_method}`);
      console.log(`   Is dynamic: ${isDynamicAssignment}`);
      
      if (routeDiscovery) {
        console.log(`   Route discovery mode: ${routeDiscovery.mode}`);
        console.log(`   Discovery type: ${routeDiscovery.discovery_type}`);
        console.log(`   Confirmed location: ${routeDiscovery.confirmed_location?.name}`);
      }
    } catch (error) {
      console.log(`❌ Failed to parse assignment notes: ${error.message}`);
    }

    // Test 4: Route Certainty Logic Simulation
    console.log('\n📊 Test 4: Route Certainty Logic Simulation');
    console.log('=' .repeat(60));

    const getLocationCertainty = (locationType, status, isDynamic) => {
      if (!isDynamic) return 'confirmed';
      
      if (locationType === 'loading') {
        return ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed'].includes(status) 
          ? 'confirmed' : 'predicted';
      } else {
        return ['unloading_start', 'unloading_end', 'trip_completed'].includes(status) 
          ? 'confirmed' : 'predicted';
      }
    };

    const loadingCertainty = getLocationCertainty('loading', tripData.status, isDynamicAssignment);
    const unloadingCertainty = getLocationCertainty('unloading', tripData.status, isDynamicAssignment);

    console.log('🎯 Route Display Logic:');
    console.log(`   Loading Location: ${loadingCertainty === 'confirmed' ? '📍' : '❓'} ${tripData.loading_location} (${loadingCertainty})`);
    console.log(`   Unloading Location: ${unloadingCertainty === 'confirmed' ? '📍' : '❓'} ${tripData.unloading_location} (${unloadingCertainty})`);
    console.log(`   Dynamic Route Indicator: ${isDynamicAssignment ? '🔄 Dynamic Route' : '📍 Traditional Route'}`);

    // Test 5: Trip Status Progression
    console.log('\n📊 Test 5: Trip Status Progression Test');
    console.log('=' .repeat(60));

    const statusProgression = ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed'];
    
    for (const status of statusProgression) {
      const loadingCert = getLocationCertainty('loading', status, isDynamicAssignment);
      const unloadingCert = getLocationCertainty('unloading', status, isDynamicAssignment);
      
      console.log(`   Status: ${status}`);
      console.log(`      Loading: ${loadingCert === 'confirmed' ? '📍' : '❓'} (${loadingCert})`);
      console.log(`      Unloading: ${unloadingCert === 'confirmed' ? '📍' : '❓'} (${unloadingCert})`);
    }

    // Test 6: Update assignment to test route discovery
    console.log('\n📊 Test 6: Route Discovery Update Test');
    console.log('=' .repeat(60));

    console.log(`🔍 Updating assignment when truck visits different unloading location...`);
    
    const updatedAssignment = await autoAssignmentCreator.updateDynamicAssignment({
      assignment: dynamicAssignment,
      location: testLocations[2], // Different unloading location
      client
    });

    console.log(`✅ Assignment updated successfully`);

    // Query updated trip data
    const updatedTripDataResult = await client.query(tripsQuery, [trip.id]);
    const updatedTripData = updatedTripDataResult.rows[0];

    console.log('📋 Updated Trip Data:');
    console.log(`   Loading Location: ${updatedTripData.loading_location}`);
    console.log(`   Unloading Location: ${updatedTripData.unloading_location}`);

    // Test 7: Performance Validation
    console.log('\n📊 Test 7: Performance Validation');
    console.log('=' .repeat(60));

    const performanceTests = [];
    
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      
      // Simulate frontend query
      await client.query(tripsQuery, [trip.id]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      performanceTests.push(duration);
    }

    const avgPerformance = performanceTests.reduce((a, b) => a + b, 0) / performanceTests.length;
    console.log(`⚡ Average query performance: ${avgPerformance.toFixed(2)}ms`);
    console.log(`   Performance target (<50ms for single trip): ${avgPerformance < 50 ? '✅ Met' : '❌ Exceeded'}`);

    // Summary
    console.log('\n🎯 Trip Monitoring Sync Test Results');
    console.log('=' .repeat(60));

    const testResults = {
      dynamicAssignmentCreation: isDynamicAssignment,
      assignmentNotesIncluded: tripData.assignment_notes !== null,
      routeDiscoveryDetection: routeDiscovery !== null,
      uncertaintyIndicators: loadingCertainty === 'confirmed' && unloadingCertainty === 'predicted',
      routeUpdateFunctionality: updatedAssignment !== null,
      performanceCompliance: avgPerformance < 50
    };

    Object.entries(testResults).forEach(([test, result]) => {
      const icon = result ? '✅' : '❌';
      const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`   ${icon} ${testName}: ${result ? 'PASS' : 'FAIL'}`);
    });

    const allTestsPass = Object.values(testResults).every(result => result);

    if (allTestsPass) {
      console.log('\n🎉 TRIP MONITORING SYNC TEST PASSED');
      console.log('✅ Trip Monitoring data table properly syncs with dynamic route discovery');
      console.log('✅ Uncertainty indicators work correctly');
      console.log('✅ Route updates are reflected in trip data');
    } else {
      console.log('\n❌ TRIP MONITORING SYNC TEST FAILED');
      console.log('⚠️  Some components are not working correctly');
    }

    return allTestsPass;

  } catch (error) {
    console.error('❌ Trip monitoring sync test failed:', error);
    return false;
  } finally {
    await client.release();
  }
}

// Run test if called directly
if (require.main === module) {
  testTripMonitoringSync()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('\n❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testTripMonitoringSync };
