const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testScannerAssignmentLogic() {
  const client = await pool.connect();
  try {
    console.log('🧪 TESTING SCANNER.JS ASSIGNMENT LOGIC CHANGES');
    console.log('=' .repeat(70));
    console.log('Testing simplified assignment validation (only "assigned" status)\n');

    // Test 1: Check current assignment statuses
    console.log('TEST 1: Current Assignment Status Analysis');
    console.log('-' .repeat(50));
    
    const statusAnalysis = await client.query(`
      SELECT
        a.assignment_code,
        a.status,
        a.truck_id,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        a.created_at,
        a.updated_at
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.created_at >= CURRENT_DATE - INTERVAL '7 days'
      ORDER BY a.created_at DESC
    `);
    
    console.log('Assignment Status Distribution:');
    statusAnalysis.rows.forEach(assignment => {
      console.log(`  ${assignment.assignment_code} (${assignment.status}):`);
      console.log(`    Truck: ${assignment.truck_number}`);
      console.log(`    Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`    Created: ${assignment.created_at}`);
      console.log(`    Updated: ${assignment.updated_at}`);
      console.log('');
    });

    // Test 2: Simulate the new assignment validation logic
    console.log('TEST 2: New Assignment Validation Logic (Assigned Status Only)');
    console.log('-' .repeat(50));
    
    const newLogicTest = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        a.notes,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name,
        CASE
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'none'
        END as location_role,
        -- Check if assignment is reusable (only assigned status)
        CASE
          WHEN a.status = 'assigned' THEN true
          ELSE false
        END as is_reusable
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
    `, ['DT-100', 1]); // Test with DT-100 and Point A (location_id = 1)
    
    console.log(`New Logic Results for DT-100 at Point A (location_id = 1):`);
    if (newLogicTest.rows.length > 0) {
      newLogicTest.rows.forEach(assignment => {
        console.log(`  ✅ FOUND: ${assignment.assignment_code} (${assignment.status})`);
        console.log(`    Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
        console.log(`    Location Role: ${assignment.location_role}`);
        console.log(`    Is Reusable: ${assignment.is_reusable}`);
        console.log('');
      });
    } else {
      console.log('  ❌ NO ASSIGNMENTS FOUND with "assigned" status for DT-100 at Point A');
    }

    // Test 3: Compare old vs new logic
    console.log('TEST 3: Old vs New Logic Comparison');
    console.log('-' .repeat(50));
    
    // Old logic (with date filtering)
    const oldLogicTest = await client.query(`
      SELECT COUNT(*) as count, 'old_logic' as type
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = $1
        AND (
          a.status IN ('assigned', 'in_progress') OR
          (a.status = 'completed' AND a.updated_at >= CURRENT_DATE - INTERVAL '1 day')
        )
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      
      UNION ALL
      
      SELECT COUNT(*) as count, 'new_logic' as type
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
    `, ['DT-100', 1]);
    
    console.log('Logic Comparison Results:');
    oldLogicTest.rows.forEach(result => {
      console.log(`  ${result.type}: ${result.count} assignments found`);
    });

    // Test 4: Test AutoAssignmentCreator logic
    console.log('\nTEST 4: AutoAssignmentCreator Reusable Assignment Check');
    console.log('-' .repeat(50));
    
    const autoAssignmentTest = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.updated_at,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [1, 1]); // truck_id = 1 (DT-100), location_id = 1 (Point A)
    
    if (autoAssignmentTest.rows.length > 0) {
      const reusableAssignment = autoAssignmentTest.rows[0];
      console.log('✅ AutoAssignmentCreator would find reusable assignment:');
      console.log(`  Assignment: ${reusableAssignment.assignment_code} (${reusableAssignment.status})`);
      console.log(`  Route: ${reusableAssignment.loading_location} → ${reusableAssignment.unloading_location}`);
      console.log(`  Updated: ${reusableAssignment.updated_at}`);
      console.log('  → This should prevent creating a new dynamic assignment');
    } else {
      console.log('❌ AutoAssignmentCreator would NOT find reusable assignment');
      console.log('  → This would trigger new dynamic assignment creation');
    }

    // Test 5: Duplicate prevention check
    console.log('\nTEST 5: Duplicate Prevention Logic');
    console.log('-' .repeat(50));
    
    const duplicateTest = await client.query(`
      SELECT COUNT(*) as duplicate_count
      FROM assignments a
      WHERE a.truck_id = $1
        AND a.loading_location_id = $2
        AND a.unloading_location_id = $2
        AND a.status = 'assigned'
    `, [1, 1]); // truck_id = 1, loading = Point A, unloading = Point A
    
    const duplicateCount = parseInt(duplicateTest.rows[0].duplicate_count);
    console.log(`Duplicate check for truck_id=1, loading=Point A, unloading=Point A:`);
    console.log(`  Found ${duplicateCount} existing assignments`);
    if (duplicateCount > 0) {
      console.log('  ✅ Duplicate prevention would BLOCK new assignment creation');
    } else {
      console.log('  ❌ Duplicate prevention would ALLOW new assignment creation');
    }

    console.log('\n🎯 TEST SUMMARY');
    console.log('=' .repeat(70));
    console.log('✅ Assignment validation now only checks "assigned" status');
    console.log('✅ Date-based filtering removed from all queries');
    console.log('✅ AutoAssignmentCreator uses simplified reusable assignment logic');
    console.log('✅ Duplicate prevention uses status-only filtering');
    console.log('✅ System will be more predictable and consistent');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testScannerAssignmentLogic().catch(console.error);
