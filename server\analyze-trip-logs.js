const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function analyzeTripLogs() {
  const client = await pool.connect();
  try {
    console.log('🔍 Analyzing trip_logs table for DT-100...\n');
    
    // Get recent trips for DT-100
    const trips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_number,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time, tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.workflow_type, tl.is_extended_trip, tl.baseline_trip_id,
        a.assignment_code, a.loading_location_id, a.unloading_location_id,
        ll.name as loading_name, ul.name as unloading_name,
        al.name as actual_loading_name, aul.name as actual_unloading_name,
        dt.truck_number
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.created_at > NOW() - INTERVAL '4 hours'
      ORDER BY tl.created_at DESC
    `);
    
    console.log('Recent trips for DT-100:');
    trips.rows.forEach((trip, index) => {
      console.log(`${index + 1}. Trip ${trip.id} (Trip #${trip.trip_number}):`);
      console.log(`   Status: ${trip.status}`);
      console.log(`   Assignment: ${trip.assignment_code}`);
      console.log(`   Route: ${trip.loading_name} → ${trip.unloading_name}`);
      console.log(`   Actual Route: ${trip.actual_loading_name || 'null'} → ${trip.actual_unloading_name || 'null'}`);
      console.log(`   Workflow: ${trip.workflow_type || 'standard'} | Extended: ${trip.is_extended_trip || false}`);
      console.log(`   Times: L(${trip.loading_start_time ? 'Y' : 'N'}-${trip.loading_end_time ? 'Y' : 'N'}) U(${trip.unloading_start_time ? 'Y' : 'N'}-${trip.unloading_end_time ? 'Y' : 'N'}) C(${trip.trip_completed_time ? 'Y' : 'N'})`);
      console.log('');
    });
    
    // Check for active trips
    const activeTrips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.trip_number,
        a.assignment_code,
        ll.name as loading_name, ul.name as unloading_name,
        dt.truck_number
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
    `);
    
    console.log('Active trips for DT-100:');
    if (activeTrips.rows.length > 0) {
      activeTrips.rows.forEach(trip => {
        console.log(`  Trip ${trip.id}: ${trip.status} | ${trip.loading_name} → ${trip.unloading_name}`);
      });
    } else {
      console.log('  No active trips found');
    }
    
    // Check assignments for DT-100
    console.log('\nAssignments for DT-100:');
    const assignments = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.assigned_date,
        ll.name as loading_name, ul.name as unloading_name,
        COUNT(tl.id) as trip_count
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      WHERE dt.truck_number = 'DT-100'
        AND a.created_at > NOW() - INTERVAL '4 hours'
      GROUP BY a.id, a.assignment_code, a.status, a.assigned_date, ll.name, ul.name
      ORDER BY a.created_at DESC
    `);
    
    assignments.rows.forEach(assignment => {
      console.log(`  Assignment ${assignment.id}: ${assignment.assignment_code} | ${assignment.status} | ${assignment.loading_name} → ${assignment.unloading_name} | ${assignment.trip_count} trips`);
    });
    
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeTripLogs().catch(console.error);
