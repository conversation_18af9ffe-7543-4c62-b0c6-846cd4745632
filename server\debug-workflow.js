const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function debugWorkflow() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Debugging Multi-Location Workflow\n');
    
    // Check recent trips and their status
    console.log('📊 Recent trips in the system:');
    const recentTrips = await client.query(`
      SELECT 
        tl.id, tl.status, tl.workflow_type, tl.is_extended_trip,
        tl.baseline_trip_id, tl.trip_completed_time,
        a.truck_id, dt.truck_number,
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.created_at > NOW() - INTERVAL '2 hours'
      ORDER BY tl.created_at DESC
      LIMIT 10
    `);
    
    recentTrips.rows.forEach(trip => {
      const indicators = [];
      if (trip.is_extended_trip) indicators.push('🔄 Extended');
      if (trip.baseline_trip_id) indicators.push(`📎 Baseline: ${trip.baseline_trip_id}`);
      
      console.log(`  Trip ${trip.id}: ${trip.truck_number} | ${trip.status} | ${trip.workflow_type} | ${trip.loading_name} → ${trip.unloading_name} | ${indicators.join(', ')}`);
    });
    
    // Test the checkRecentCompletedTrip function
    console.log('\n🔍 Testing checkRecentCompletedTrip function:');
    
    // Get a truck that has completed trips
    const truckWithCompletedTrip = await client.query(`
      SELECT DISTINCT dt.id, dt.truck_number
      FROM dump_trucks dt
      JOIN assignments a ON dt.id = a.truck_id
      JOIN trip_logs tl ON a.id = tl.assignment_id
      WHERE tl.status = 'trip_completed'
        AND tl.trip_completed_time > NOW() - INTERVAL '1 hour'
      LIMIT 1
    `);
    
    if (truckWithCompletedTrip.rows.length > 0) {
      const truck = truckWithCompletedTrip.rows[0];
      console.log(`  Testing with truck: ${truck.truck_number}`);
      
      // Get a loading location
      const loadingLocation = await client.query(`
        SELECT id, name, type, location_code
        FROM locations 
        WHERE type = 'loading'
        LIMIT 1
      `);
      
      if (loadingLocation.rows.length > 0) {
        const location = loadingLocation.rows[0];
        console.log(`  Testing with location: ${location.name} (${location.type})`);
        
        // Import the function and test it
        const { checkRecentCompletedTrip } = require('./routes/scanner');
        
        const recentTrip = await checkRecentCompletedTrip(client, truck.id, location);
        
        console.log(`  Recent completed trip found: ${!!recentTrip}`);
        if (recentTrip) {
          console.log(`    Trip ID: ${recentTrip.id}`);
          console.log(`    Status: ${recentTrip.status}`);
          console.log(`    Completed: ${recentTrip.trip_completed_time}`);
          console.log(`    Route: ${recentTrip.loading_location_name} → ${recentTrip.unloading_location_name}`);
        }
        
        // Test workflow type determination
        if (recentTrip) {
          const { determineWorkflowType } = require('./routes/scanner');
          const workflowType = determineWorkflowType(recentTrip, location);
          console.log(`    Workflow type: ${workflowType}`);
        }
      }
    } else {
      console.log('  No trucks with recent completed trips found');
    }
    
    // Check for any active trips
    console.log('\n🔍 Active trips in the system:');
    const activeTrips = await client.query(`
      SELECT 
        tl.id, tl.status, dt.truck_number,
        ll.name as loading_name, ul.name as unloading_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
    `);
    
    if (activeTrips.rows.length > 0) {
      activeTrips.rows.forEach(trip => {
        console.log(`  Trip ${trip.id}: ${trip.truck_number} | ${trip.status} | ${trip.loading_name} → ${trip.unloading_name}`);
      });
    } else {
      console.log('  No active trips found');
    }
    
    console.log('\n🎉 Debug Complete!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

debugWorkflow().catch(console.error);
