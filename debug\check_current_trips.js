const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function checkCurrentTrips() {
  const client = await pool.connect();
  try {
    const result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.created_at,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.loading_start_time, tl.unloading_start_time,
        dt.truck_number,
        a.assignment_code, a.notes as assignment_notes,
        ll.name as loading_location,
        ul.name as unloading_location,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 5
    `);
    
    console.log('Current trips:');
    result.rows.forEach(trip => {
      console.log(`Trip #${trip.trip_number} (${trip.truck_number}): ${trip.status}`);
      console.log(`  Assignment: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`  Actual: ${trip.actual_loading_location || 'None'} → ${trip.actual_unloading_location || 'None'}`);
      console.log(`  Assignment Notes: ${trip.assignment_notes ? 'Present' : 'None'}`);
      if (trip.assignment_notes) {
        try {
          const notes = JSON.parse(trip.assignment_notes);
          console.log(`  Creation Method: ${notes.creation_method || 'Unknown'}`);
        } catch (e) {
          console.log(`  Notes parsing failed`);
        }
      }
      console.log('');
    });

    // Check location types
    console.log('\nLocation types:');
    const locations = await client.query(`
      SELECT id, name, type FROM locations ORDER BY id
    `);
    locations.rows.forEach(loc => {
      console.log(`  ${loc.id}: ${loc.name} (${loc.type})`);
    });

  } finally {
    client.release();
    await pool.end();
  }
}

checkCurrentTrips().catch(console.error);
