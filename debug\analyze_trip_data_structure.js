const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeTripDataStructure() {
  const client = await pool.connect();
  try {
    console.log('🔍 Analyzing Trip Data Structure for Status Display Logic...');

    // Get Trip #2 (ID: 104) to understand the data structure
    const trip2Result = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time, tl.total_duration_minutes,
        tl.notes as trip_notes,
        a.notes as assignment_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.id = 104
    `);

    if (trip2Result.rows.length === 0) {
      console.log('❌ Trip #2 (ID: 104) not found');
      return;
    }

    const trip2 = trip2Result.rows[0];
    console.log('\n📊 Trip #2 Data Structure Analysis:');
    console.log('=' .repeat(50));
    console.log(`Trip ID: ${trip2.id}`);
    console.log(`Trip Number: ${trip2.trip_number}`);
    console.log(`Database Status: ${trip2.status}`);
    console.log('');

    console.log('⏰ Timestamp Analysis:');
    console.log(`Loading Start: ${trip2.loading_start_time || 'NULL'} ${trip2.loading_start_time ? '✅' : '❌'}`);
    console.log(`Loading End: ${trip2.loading_end_time || 'NULL'} ${trip2.loading_end_time ? '✅' : '❌'}`);
    console.log(`Unloading Start: ${trip2.unloading_start_time || 'NULL'} ${trip2.unloading_start_time ? '✅' : '❌'}`);
    console.log(`Unloading End: ${trip2.unloading_end_time || 'NULL'} ${trip2.unloading_end_time ? '✅' : '❌'}`);
    console.log(`Trip Completed: ${trip2.trip_completed_time || 'NULL'} ${trip2.trip_completed_time ? '✅' : '❌'}`);
    console.log('');

    // Analyze what the actual status should be based on timestamps
    console.log('🎯 Actual Trip Progression Analysis:');
    const hasLoadingStart = !!trip2.loading_start_time;
    const hasLoadingEnd = !!trip2.loading_end_time;
    const hasUnloadingStart = !!trip2.unloading_start_time;
    const hasUnloadingEnd = !!trip2.unloading_end_time;
    const hasCompletion = !!trip2.trip_completed_time;

    let actualStatus = 'assigned';
    let actualStatusLabel = 'Assigned';

    if (hasCompletion && hasUnloadingEnd && hasUnloadingStart && hasLoadingEnd && hasLoadingStart) {
      actualStatus = 'trip_completed';
      actualStatusLabel = 'Completed';
    } else if (hasUnloadingEnd && hasUnloadingStart && hasLoadingEnd && hasLoadingStart) {
      actualStatus = 'unloading_end';
      actualStatusLabel = 'Unloading Complete';
    } else if (hasUnloadingStart && hasLoadingEnd && hasLoadingStart) {
      actualStatus = 'unloading_start';
      actualStatusLabel = 'Unloading Started';
    } else if (hasLoadingEnd && hasLoadingStart) {
      actualStatus = 'loading_end';
      actualStatusLabel = 'Loading Complete';
    } else if (hasLoadingStart) {
      actualStatus = 'loading_start';
      actualStatusLabel = 'Loading Started';
    }

    console.log(`Database Status: ${trip2.status}`);
    console.log(`Actual Status (based on timestamps): ${actualStatus}`);
    console.log(`Should Display: ${actualStatusLabel}`);
    console.log('');

    // Check if this is an auto-completed trip
    let isAutoCompleted = false;
    let completionMethod = 'unknown';
    if (trip2.trip_notes) {
      try {
        const notes = JSON.parse(trip2.trip_notes);
        if (notes.completion_method) {
          completionMethod = notes.completion_method;
          isAutoCompleted = notes.completion_method.includes('auto_completed');
        }
      } catch (e) {
        console.log('❌ Failed to parse trip notes');
      }
    }

    console.log('🔍 Auto-Completion Analysis:');
    console.log(`Completion Method: ${completionMethod}`);
    console.log(`Is Auto-Completed: ${isAutoCompleted ? 'Yes' : 'No'}`);
    console.log('');

    // Determine the correct status display logic
    console.log('✅ Correct Status Display Logic:');
    console.log('=' .repeat(40));

    if (trip2.status === 'trip_completed') {
      if (isAutoCompleted) {
        // For auto-completed trips, show status based on actual progression
        if (hasLoadingEnd && !hasUnloadingStart) {
          console.log('✅ Should show: "Loading Completed (Auto-completed)"');
        } else if (hasUnloadingEnd && !hasCompletion) {
          console.log('✅ Should show: "Unloading Completed (Auto-completed)"');
        } else {
          console.log('✅ Should show: "Completed (Auto-completed)"');
        }
      } else {
        // For naturally completed trips, show normal completion
        console.log('✅ Should show: "Completed"');
      }
    } else {
      console.log(`✅ Should show: "${actualStatusLabel}"`);
    }

    console.log('');
    console.log('📋 Implementation Requirements:');
    console.log('1. Check if trip is auto-completed (trip_notes.completion_method)');
    console.log('2. For auto-completed trips, determine actual progression from timestamps');
    console.log('3. Show appropriate status: "Loading Completed", "Unloading Completed", or "Completed"');
    console.log('4. Add "(Auto-completed)" suffix for auto-completed trips');

    // Test with a few more trips to understand the pattern
    console.log('\n🧪 Testing with other recent trips...');
    const otherTrips = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.notes as trip_notes
      FROM trip_logs tl
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
        AND tl.id != 104
      ORDER BY tl.created_at DESC
      LIMIT 3
    `);

    otherTrips.rows.forEach((trip, index) => {
      console.log(`\nTrip #${trip.trip_number} (ID: ${trip.id}):`);
      console.log(`  Database Status: ${trip.status}`);
      
      const hasLoading = !!trip.loading_start_time && !!trip.loading_end_time;
      const hasUnloading = !!trip.unloading_start_time && !!trip.unloading_end_time;
      const hasCompletion = !!trip.trip_completed_time;
      
      let isAutoCompleted = false;
      if (trip.trip_notes) {
        try {
          const notes = JSON.parse(trip.trip_notes);
          isAutoCompleted = notes.completion_method && notes.completion_method.includes('auto_completed');
        } catch (e) {
          // Ignore
        }
      }
      
      console.log(`  Has Loading: ${hasLoading ? 'Yes' : 'No'}`);
      console.log(`  Has Unloading: ${hasUnloading ? 'Yes' : 'No'}`);
      console.log(`  Auto-completed: ${isAutoCompleted ? 'Yes' : 'No'}`);
      
      if (trip.status === 'trip_completed' && isAutoCompleted) {
        if (hasLoading && !hasUnloading) {
          console.log(`  ✅ Should show: "Loading Completed (Auto-completed)"`);
        } else if (hasUnloading) {
          console.log(`  ✅ Should show: "Completed (Auto-completed)"`);
        }
      } else {
        console.log(`  ✅ Should show: Normal status display`);
      }
    });

  } catch (error) {
    console.error('❌ Error analyzing trip data structure:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeTripDataStructure().catch(console.error);
