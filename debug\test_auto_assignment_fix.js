const { Pool } = require('pg');
const { AutoAssignmentCreator } = require('../server/utils/AutoAssignmentCreator');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testAutoAssignmentFix() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 TESTING AUTO-ASSIGNMENT CREATION FIX');
    console.log('=' .repeat(60));
    
    // Test the specific scenario: DT-100 at Point C - Secondary Dump Site
    console.log('\n📍 Test Scenario: DT-100 at Point C - Secondary Dump Site');
    console.log('-' .repeat(50));
    
    // Get truck data
    const truckResult = await client.query(`
      SELECT id, truck_number, status FROM dump_trucks WHERE truck_number = $1
    `, ['DT-100']);
    
    if (truckResult.rows.length === 0) {
      console.log('❌ Truck DT-100 not found');
      return;
    }
    
    const truck = truckResult.rows[0];
    console.log(`✅ Found truck: ${truck.truck_number} (ID: ${truck.id}, Status: ${truck.status})`);
    
    // Get Point C location data
    const locationResult = await client.query(`
      SELECT id, name, type, location_code FROM locations WHERE location_code = $1
    `, ['LOC-003']);
    
    if (locationResult.rows.length === 0) {
      console.log('❌ Point C - Secondary Dump Site (LOC-003) not found');
      return;
    }
    
    const location = locationResult.rows[0];
    console.log(`✅ Found location: ${location.name} (ID: ${location.id}, Type: ${location.type})`);
    
    // Test the AutoAssignmentCreator shouldCreateAutoAssignment method
    console.log('\n🔍 Testing shouldCreateAutoAssignment method...');
    const autoAssignmentCreator = new AutoAssignmentCreator();
    
    const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
      truck,
      location,
      client
    });
    
    console.log('📊 shouldCreateAutoAssignment result:');
    console.log(`   Should Create: ${shouldCreateCheck.shouldCreate}`);
    console.log(`   Reason: ${shouldCreateCheck.reason}`);
    console.log(`   Recommendation: ${shouldCreateCheck.recommendation}`);
    
    if (shouldCreateCheck.existingAssignment) {
      console.log(`   Existing Assignment: ${shouldCreateCheck.existingAssignment.assignment_code}`);
    }
    
    // If it should create, test the actual creation
    if (shouldCreateCheck.shouldCreate) {
      console.log('\n🚀 Testing auto-assignment creation...');
      
      try {
        const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
          truck,
          location,
          client,
          userId: 1, // Test user ID
          enableDynamicRouting: true
        });
        
        console.log('✅ Auto-assignment created successfully!');
        console.log(`   Assignment Code: ${autoAssignment.assignment_code}`);
        console.log(`   Assignment ID: ${autoAssignment.id}`);
        console.log(`   Status: ${autoAssignment.status}`);
        console.log(`   Loading Location: ${autoAssignment.loading_location_name || 'TBD'}`);
        console.log(`   Unloading Location: ${autoAssignment.unloading_location_name || 'TBD'}`);
        
        // Clean up - delete the test assignment
        await client.query('DELETE FROM assignments WHERE id = $1', [autoAssignment.id]);
        console.log('🧹 Test assignment cleaned up');
        
      } catch (creationError) {
        console.log('❌ Auto-assignment creation failed:');
        console.log(`   Error: ${creationError.message}`);
        
        // Check if this is the original parameter binding error
        if (creationError.message.includes('bind message supplies') && creationError.message.includes('parameters')) {
          console.log('🚨 PARAMETER BINDING ERROR STILL EXISTS!');
          console.log('   The fix may not have been applied correctly.');
        }
      }
    } else {
      console.log('ℹ️  Auto-assignment creation not needed (existing assignment found or other reason)');
    }
    
    console.log('\n✅ Test completed successfully');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

testAutoAssignmentFix().catch(console.error);
