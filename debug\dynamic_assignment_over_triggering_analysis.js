const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function analyzeDynamicAssignmentOverTriggering() {
  const client = await pool.connect();
  try {
    console.log('🔍 DYNAMIC ASSIGNMENT LOGIC OVER-TRIGGERING ANALYSIS');
    console.log('=' .repeat(80));

    // 1. Analyze current assignment validation logic effectiveness
    console.log('\n📋 ASSIGNMENT VALIDATION LOGIC ANALYSIS');
    console.log('-' .repeat(60));
    
    // Test the current assignment validation query from scanner.js
    const assignmentValidationTest = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        a.notes,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name,
        CASE
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'none'
        END as location_role,
        CASE
          WHEN a.status = 'assigned' THEN true
          ELSE false
        END as is_reusable
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
    `, ['DT-100', 1]); // Test with DT-100 and Point A (location_id = 1)
    
    console.log(`Assignment Validation Query Results for DT-100 at Point A:`);
    if (assignmentValidationTest.rows.length > 0) {
      assignmentValidationTest.rows.forEach(assignment => {
        console.log(`  ✅ FOUND: ${assignment.assignment_code} (${assignment.status})`);
        console.log(`    Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
        console.log(`    Location Role: ${assignment.location_role}`);
        console.log(`    Is Reusable: ${assignment.is_reusable}`);
        console.log(`    Created: ${assignment.assigned_date}`);
        console.log('');
      });
    } else {
      console.log('  ❌ NO ASSIGNMENTS FOUND - This would trigger AutoAssignmentCreator');
    }

    // 2. Analyze AutoAssignmentCreator shouldCreateAutoAssignment logic
    console.log('\n📋 AUTOASSIGNMENTCREATOR TRIGGER ANALYSIS');
    console.log('-' .repeat(60));
    
    // Test the reusable assignment check from AutoAssignmentCreator
    const reusableAssignmentCheck = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.updated_at,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [1, 1]); // truck_id = 1 (DT-100), location_id = 1 (Point A)
    
    console.log('AutoAssignmentCreator Reusable Assignment Check:');
    if (reusableAssignmentCheck.rows.length > 0) {
      const reusableAssignment = reusableAssignmentCheck.rows[0];
      console.log('  ✅ REUSABLE ASSIGNMENT FOUND:');
      console.log(`    Assignment: ${reusableAssignment.assignment_code} (${reusableAssignment.status})`);
      console.log(`    Route: ${reusableAssignment.loading_location} → ${reusableAssignment.unloading_location}`);
      console.log(`    Updated: ${reusableAssignment.updated_at}`);
      console.log('    → shouldCreateAutoAssignment would return FALSE (no new assignment needed)');
    } else {
      console.log('  ❌ NO REUSABLE ASSIGNMENT FOUND');
      console.log('    → shouldCreateAutoAssignment would return TRUE (create new assignment)');
    }

    // 3. Analyze historical assignment patterns to identify over-triggering
    console.log('\n📋 HISTORICAL ASSIGNMENT CREATION PATTERN ANALYSIS');
    console.log('-' .repeat(60));
    
    const assignmentCreationPattern = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        a.notes,
        a.created_at,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        COUNT(tl.id) as trip_count,
        CASE 
          WHEN a.notes::text LIKE '%dynamic_assignment%' THEN 'Dynamic'
          WHEN a.notes::text LIKE '%auto_assignment%' THEN 'Auto'
          ELSE 'Manual'
        END as creation_type
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
      WHERE a.created_at >= CURRENT_DATE - INTERVAL '2 days'
      GROUP BY a.id, a.assignment_code, a.status, a.notes, a.created_at, dt.truck_number, ll.name, ul.name
      ORDER BY a.created_at DESC
    `);
    
    console.log('Assignment Creation Patterns (Last 2 days):');
    let dynamicCount = 0;
    let autoCount = 0;
    let manualCount = 0;
    let potentialOverTriggers = [];
    
    assignmentCreationPattern.rows.forEach(assignment => {
      console.log(`\n  ${assignment.assignment_code} (${assignment.creation_type}):`);
      console.log(`    Truck: ${assignment.truck_number}`);
      console.log(`    Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`    Status: ${assignment.status}`);
      console.log(`    Trips: ${assignment.trip_count}`);
      console.log(`    Created: ${assignment.created_at}`);
      
      if (assignment.creation_type === 'Dynamic') {
        dynamicCount++;
      } else if (assignment.creation_type === 'Auto') {
        autoCount++;
      } else {
        manualCount++;
      }
      
      // Check for potential over-triggering (multiple assignments for same truck+route)
      const routeKey = `${assignment.truck_number}-${assignment.loading_location}-${assignment.unloading_location}`;
      const existingRoute = potentialOverTriggers.find(p => p.routeKey === routeKey);
      if (existingRoute) {
        existingRoute.count++;
        existingRoute.assignments.push(assignment);
      } else {
        potentialOverTriggers.push({
          routeKey,
          count: 1,
          assignments: [assignment]
        });
      }
    });
    
    console.log(`\nAssignment Creation Summary:`);
    console.log(`  Dynamic Assignments: ${dynamicCount}`);
    console.log(`  Auto Assignments: ${autoCount}`);
    console.log(`  Manual Assignments: ${manualCount}`);
    console.log(`  Total: ${assignmentCreationPattern.rows.length}`);

    // 4. Identify potential over-triggering scenarios
    console.log('\n📋 OVER-TRIGGERING SCENARIO ANALYSIS');
    console.log('-' .repeat(60));
    
    const overTriggerScenarios = potentialOverTriggers.filter(p => p.count > 1);
    if (overTriggerScenarios.length > 0) {
      console.log('⚠️ POTENTIAL OVER-TRIGGERING DETECTED:');
      overTriggerScenarios.forEach(scenario => {
        console.log(`\n  Route: ${scenario.routeKey} (${scenario.count} assignments)`);
        scenario.assignments.forEach((assignment, index) => {
          console.log(`    ${index + 1}. ${assignment.assignment_code} (${assignment.creation_type}) - ${assignment.created_at}`);
        });
        console.log(`    🔍 Analysis: Multiple assignments for same truck+route may indicate over-triggering`);
      });
    } else {
      console.log('✅ No over-triggering scenarios detected (each truck+route has single assignment)');
    }

    // 5. Test duplicate prevention logic
    console.log('\n📋 DUPLICATE PREVENTION LOGIC ANALYSIS');
    console.log('-' .repeat(60));
    
    const duplicatePreventionTest = await client.query(`
      SELECT COUNT(*) as duplicate_count
      FROM assignments a
      WHERE a.truck_id = $1
        AND a.loading_location_id = $2
        AND a.unloading_location_id = $2
        AND a.status = 'assigned'
    `, [1, 1]); // truck_id = 1, loading = Point A, unloading = Point A
    
    const duplicateCount = parseInt(duplicatePreventionTest.rows[0].duplicate_count);
    console.log(`Duplicate Prevention Check (truck_id=1, loading=Point A, unloading=Point A):`);
    console.log(`  Found ${duplicateCount} existing assignments`);
    if (duplicateCount > 0) {
      console.log('  ✅ Duplicate prevention would BLOCK new assignment creation');
    } else {
      console.log('  ❌ Duplicate prevention would ALLOW new assignment creation');
    }

    // 6. Analyze timing and race condition potential
    console.log('\n📋 TIMING AND RACE CONDITION ANALYSIS');
    console.log('-' .repeat(60));
    
    const timingAnalysis = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.created_at,
        dt.truck_number,
        LAG(a.created_at) OVER (PARTITION BY dt.truck_number ORDER BY a.created_at) as prev_assignment_time,
        EXTRACT(EPOCH FROM (a.created_at - LAG(a.created_at) OVER (PARTITION BY dt.truck_number ORDER BY a.created_at))) as seconds_since_prev
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE a.created_at >= CURRENT_DATE - INTERVAL '2 days'
      ORDER BY dt.truck_number, a.created_at
    `);
    
    console.log('Assignment Creation Timing Analysis:');
    timingAnalysis.rows.forEach(assignment => {
      if (assignment.seconds_since_prev !== null) {
        const timeDiff = parseFloat(assignment.seconds_since_prev);
        console.log(`\n  ${assignment.assignment_code} (${assignment.truck_number}):`);
        console.log(`    Created: ${assignment.created_at}`);
        console.log(`    Time since previous: ${timeDiff.toFixed(1)} seconds`);
        
        if (timeDiff < 60) {
          console.log(`    ⚠️ POTENTIAL RACE CONDITION: Assignment created within 60 seconds of previous`);
        } else if (timeDiff < 300) {
          console.log(`    ⚠️ RAPID CREATION: Assignment created within 5 minutes of previous`);
        } else {
          console.log(`    ✅ Normal timing gap`);
        }
      }
    });

    // 7. Simulate the complete validation flow
    console.log('\n📋 COMPLETE VALIDATION FLOW SIMULATION');
    console.log('-' .repeat(60));
    
    console.log('Simulating scanner.js processTruckScan validation flow:');
    console.log('\nStep 1: Check for valid assignments (scanner.js logic)');
    const step1Result = assignmentValidationTest.rows.length > 0;
    console.log(`  Result: ${step1Result ? 'FOUND' : 'NOT FOUND'}`);
    
    if (!step1Result) {
      console.log('\nStep 2: AutoAssignmentCreator.shouldCreateAutoAssignment()');
      console.log('  Check 1: Look for reusable assignments');
      const step2Check1 = reusableAssignmentCheck.rows.length > 0;
      console.log(`    Result: ${step2Check1 ? 'FOUND (should not create)' : 'NOT FOUND (continue checks)'}`);
      
      if (!step2Check1) {
        console.log('  Check 2: Historical assignments exist');
        const historicalCheck = await client.query(`
          SELECT COUNT(*) as count FROM assignments WHERE truck_id = $1
        `, [1]);
        const hasHistorical = parseInt(historicalCheck.rows[0].count) > 0;
        console.log(`    Result: ${hasHistorical ? 'YES (continue)' : 'NO (block creation)'}`);
        
        console.log('  Check 3: Location type valid');
        console.log('    Result: YES (loading/unloading location)');
        
        console.log('  Check 4: Truck active');
        console.log('    Result: YES (truck is active)');
        
        console.log('  Check 5: Duplicate prevention');
        console.log(`    Result: ${duplicateCount > 0 ? 'DUPLICATE FOUND (block creation)' : 'NO DUPLICATE (allow creation)'}`);
        
        const finalDecision = hasHistorical && duplicateCount === 0;
        console.log(`\n  Final Decision: ${finalDecision ? 'CREATE DYNAMIC ASSIGNMENT' : 'DO NOT CREATE'}`);
      } else {
        console.log('\n  Final Decision: REUSE EXISTING ASSIGNMENT (no creation needed)');
      }
    } else {
      console.log('\n  Final Decision: USE FOUND ASSIGNMENT (no creation needed)');
    }

    console.log('\n🎯 OVER-TRIGGERING ANALYSIS SUMMARY');
    console.log('=' .repeat(80));
    console.log(`✅ Assignment validation logic: ${step1Result ? 'Working correctly' : 'May need enhancement'}`);
    console.log(`✅ AutoAssignmentCreator reuse check: ${reusableAssignmentCheck.rows.length > 0 ? 'Working correctly' : 'May miss assignments'}`);
    console.log(`✅ Duplicate prevention: ${duplicateCount > 0 ? 'Working correctly' : 'May allow duplicates'}`);
    console.log(`📊 Over-triggering scenarios: ${overTriggerScenarios.length} detected`);
    console.log(`📊 Assignment creation rate: ${assignmentCreationPattern.rows.length} assignments in 2 days`);
    console.log(`🔍 Root cause: ${overTriggerScenarios.length > 0 ? 'Assignment validation logic may be missing valid assignments' : 'System appears to be working correctly'}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

analyzeDynamicAssignmentOverTriggering().catch(console.error);
