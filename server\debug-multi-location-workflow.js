#!/usr/bin/env node

/**
 * Debug Multi-Location Workflow
 * 
 * This script helps debug the multi-location workflow by checking:
 * 1. Database schema
 * 2. Sample data
 * 3. API response format
 * 4. Frontend parsing logic
 */

const { Pool } = require('pg');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
};

async function debugMultiLocationWorkflow() {
  console.log('🔍 Debugging Multi-Location Workflow...\n');

  const pool = new Pool(dbConfig);
  const client = await pool.connect();

  try {
    // 1. Check database schema
    console.log('1️⃣ Checking Database Schema...');
    const schemaResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'trip_logs'
        AND column_name IN ('location_sequence', 'is_extended_trip', 'workflow_type', 'baseline_trip_id', 'cycle_number')
      ORDER BY column_name;
    `);

    if (schemaResult.rows.length === 0) {
      console.log('❌ Multi-location workflow columns NOT FOUND!');
      console.log('   The migration may not have been applied.');
      return;
    } else {
      console.log('✅ Multi-location workflow columns found:');
      schemaResult.rows.forEach(row => {
        console.log(`   ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
      });
    }

    // 2. Check for existing multi-location data
    console.log('\n2️⃣ Checking for Multi-Location Trip Data...');
    const dataResult = await client.query(`
      SELECT 
        id, 
        trip_number,
        status,
        location_sequence,
        is_extended_trip,
        workflow_type,
        baseline_trip_id,
        cycle_number,
        pg_typeof(location_sequence) as location_sequence_type
      FROM trip_logs 
      WHERE location_sequence IS NOT NULL 
         OR is_extended_trip = true 
         OR workflow_type != 'standard'
      ORDER BY created_at DESC
      LIMIT 5;
    `);

    if (dataResult.rows.length === 0) {
      console.log('⚠️  No multi-location trip data found.');
      console.log('   This could be why the workflow isn\'t displaying.');
    } else {
      console.log('✅ Multi-location trip data found:');
      dataResult.rows.forEach(row => {
        console.log(`   Trip ${row.id}:`);
        console.log(`     Status: ${row.status}`);
        console.log(`     Extended: ${row.is_extended_trip}`);
        console.log(`     Workflow: ${row.workflow_type}`);
        console.log(`     Location Sequence Type: ${row.location_sequence_type}`);
        console.log(`     Location Sequence: ${JSON.stringify(row.location_sequence)}`);
        console.log('');
      });
    }

    // 3. Check recent trips to see if they have location_sequence populated
    console.log('3️⃣ Checking Recent Trips for Location Sequence...');
    const recentTripsResult = await client.query(`
      SELECT 
        id, 
        status,
        location_sequence,
        workflow_type,
        loading_start_time,
        unloading_start_time,
        trip_completed_time
      FROM trip_logs 
      ORDER BY created_at DESC
      LIMIT 10;
    `);

    console.log('Recent trips location_sequence status:');
    recentTripsResult.rows.forEach(row => {
      const hasLocationSequence = row.location_sequence !== null;
      const workflowType = row.workflow_type || 'standard';
      console.log(`   Trip ${row.id}: ${row.status} | location_sequence: ${hasLocationSequence ? '✅' : '❌'} | workflow: ${workflowType}`);
    });

    // 4. Test the location sequence update function
    console.log('\n4️⃣ Testing Location Sequence Update...');
    
    // Find a completed trip without location_sequence
    const tripToUpdateResult = await client.query(`
      SELECT tl.*, a.loading_location_id, a.unloading_location_id,
             ll.name as loading_location_name, ul.name as unloading_location_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status = 'trip_completed' 
        AND tl.location_sequence IS NULL
      ORDER BY tl.created_at DESC
      LIMIT 1;
    `);

    if (tripToUpdateResult.rows.length > 0) {
      const trip = tripToUpdateResult.rows[0];
      console.log(`Found trip ${trip.id} to update with location sequence...`);
      
      // Create location sequence
      const locationSequence = [
        {
          name: trip.loading_location_name || 'Unknown Loading',
          type: 'loading',
          confirmed: !!trip.loading_start_time,
          location_id: trip.loading_location_id
        },
        {
          name: trip.unloading_location_name || 'Unknown Unloading',
          type: 'unloading',
          confirmed: !!trip.unloading_start_time,
          location_id: trip.unloading_location_id
        }
      ];

      // Update the trip
      await client.query(`
        UPDATE trip_logs 
        SET location_sequence = $1,
            workflow_type = 'standard',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [JSON.stringify(locationSequence), trip.id]);

      console.log(`✅ Updated trip ${trip.id} with location sequence:`, locationSequence);
    } else {
      console.log('⚠️  No completed trips found without location_sequence to update.');
    }

    // 5. Summary and recommendations
    console.log('\n5️⃣ Summary and Recommendations...');
    
    const totalTrips = await client.query('SELECT COUNT(*) as count FROM trip_logs');
    const tripsWithLocationSequence = await client.query('SELECT COUNT(*) as count FROM trip_logs WHERE location_sequence IS NOT NULL');
    const extendedTrips = await client.query('SELECT COUNT(*) as count FROM trip_logs WHERE is_extended_trip = true');

    console.log(`📊 Statistics:`);
    console.log(`   Total trips: ${totalTrips.rows[0].count}`);
    console.log(`   Trips with location_sequence: ${tripsWithLocationSequence.rows[0].count}`);
    console.log(`   Extended trips: ${extendedTrips.rows[0].count}`);

    const percentageWithLocationSequence = (tripsWithLocationSequence.rows[0].count / totalTrips.rows[0].count * 100).toFixed(1);
    console.log(`   Coverage: ${percentageWithLocationSequence}% of trips have location_sequence`);

    if (percentageWithLocationSequence < 50) {
      console.log('\n⚠️  ISSUE IDENTIFIED: Low location_sequence coverage');
      console.log('   Recommendation: Run migration to populate location_sequence for existing trips');
    } else {
      console.log('\n✅ Good location_sequence coverage');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the debug
if (require.main === module) {
  debugMultiLocationWorkflow().catch(console.error);
}

module.exports = { debugMultiLocationWorkflow };
