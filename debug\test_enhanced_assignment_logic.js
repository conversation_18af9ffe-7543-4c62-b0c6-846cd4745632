const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function testEnhancedAssignmentLogic() {
  const client = await pool.connect();
  try {
    console.log('🧪 Testing Enhanced Assignment Logic...\n');

    // Test 1: Check if enhanced assignment validation finds reusable assignments
    console.log('TEST 1: Enhanced Assignment Validation');
    console.log('=' .repeat(50));
    
    const enhancedValidationResult = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        a.notes,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name,
        CASE
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'none'
        END as location_role,
        -- Check if assignment has recent activity (within last 24 hours)
        CASE 
          WHEN EXISTS (
            SELECT 1 FROM trip_logs tl 
            WHERE tl.assignment_id = a.id 
              AND tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
          ) THEN true 
          ELSE false 
        END as has_recent_activity
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND (
          -- Active assignments
          a.status IN ('assigned', 'in_progress') OR
          -- Recently completed assignments that can be reused (within 24 hours)
          (a.status = 'completed' AND a.updated_at >= CURRENT_DATE - INTERVAL '1 day')
        )
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY 
        -- Prioritize active assignments, then recent ones
        CASE WHEN a.status IN ('assigned', 'in_progress') THEN 1 ELSE 2 END,
        a.updated_at DESC
    `, ['DT-100', 1]); // Point A - Main Loading Site

    console.log(`Found ${enhancedValidationResult.rows.length} reusable assignments for DT-100 at Point A:`);
    enhancedValidationResult.rows.forEach(assignment => {
      let isDynamic = false;
      try {
        const notes = JSON.parse(assignment.notes || '{}');
        isDynamic = notes.creation_method === 'dynamic_assignment';
      } catch (e) {
        // Ignore
      }
      
      console.log(`  - ${assignment.assignment_code} (${assignment.status}) ${isDynamic ? '🔄' : '📍'}`);
      console.log(`    Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`    Recent Activity: ${assignment.has_recent_activity ? 'Yes' : 'No'}`);
      console.log(`    Location Role: ${assignment.location_role}`);
    });

    // Test 2: Check trip number generation logic
    console.log('\nTEST 2: Trip Number Generation Logic');
    console.log('=' .repeat(50));
    
    // Simulate the new trip number generation logic
    const truckId = 1; // DT-100
    const tripNumberResult = await client.query(`
      SELECT COALESCE(MAX(tl.trip_number), 0) + 1 as next_number
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND DATE(tl.created_at) = CURRENT_DATE
    `, [truckId]);
    
    console.log(`Next trip number for DT-100 today: ${tripNumberResult.rows[0].next_number}`);
    
    // Show current trip numbers for today
    const currentTripsResult = await client.query(`
      SELECT 
        tl.trip_number,
        a.assignment_code,
        tl.status,
        tl.created_at
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND DATE(tl.created_at) = CURRENT_DATE
      ORDER BY tl.created_at ASC
    `, [truckId]);
    
    console.log(`Current trips for DT-100 today:`);
    currentTripsResult.rows.forEach(trip => {
      console.log(`  Trip #${trip.trip_number}: ${trip.assignment_code} (${trip.status})`);
    });

    // Test 3: Check for duplicate trip numbers
    console.log('\nTEST 3: Duplicate Trip Number Check');
    console.log('=' .repeat(50));
    
    const duplicateCheckResult = await client.query(`
      SELECT 
        tl.trip_number,
        COUNT(*) as occurrence_count,
        STRING_AGG(DISTINCT a.assignment_code, ', ') as assignments
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
        AND DATE(tl.created_at) = CURRENT_DATE
      GROUP BY tl.trip_number
      HAVING COUNT(*) > 1
      ORDER BY tl.trip_number
    `);

    if (duplicateCheckResult.rows.length > 0) {
      console.log('⚠️ DUPLICATE TRIP NUMBERS FOUND:');
      duplicateCheckResult.rows.forEach(row => {
        console.log(`  Trip #${row.trip_number}: ${row.occurrence_count} occurrences in assignments: ${row.assignments}`);
      });
    } else {
      console.log('✅ No duplicate trip numbers found for DT-100 today');
    }

    // Test 4: Verify assignment reuse logic
    console.log('\nTEST 4: Assignment Reuse Logic Verification');
    console.log('=' .repeat(50));
    
    const reusableAssignmentsResult = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.updated_at,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND (
          -- Active assignments
          a.status IN ('assigned', 'in_progress') OR
          -- Recently completed assignments (within 24 hours) that could be reused
          (a.status = 'completed' AND a.updated_at >= CURRENT_DATE - INTERVAL '1 day')
        )
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY 
        CASE WHEN a.status IN ('assigned', 'in_progress') THEN 1 ELSE 2 END,
        a.updated_at DESC
      LIMIT 1
    `, [truckId, 1]); // Point A

    if (reusableAssignmentsResult.rows.length > 0) {
      const reusableAssignment = reusableAssignmentsResult.rows[0];
      console.log('✅ Found reusable assignment:');
      console.log(`  Assignment: ${reusableAssignment.assignment_code} (${reusableAssignment.status})`);
      console.log(`  Route: ${reusableAssignment.loading_location} → ${reusableAssignment.unloading_location}`);
      console.log(`  Updated: ${reusableAssignment.updated_at}`);
      console.log('  → This should prevent creating a new dynamic assignment');
    } else {
      console.log('❌ No reusable assignments found - would create new dynamic assignment');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testEnhancedAssignmentLogic().catch(console.error);
