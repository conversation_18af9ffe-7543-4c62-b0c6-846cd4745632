const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

// Import the scanner function for testing
const path = require('path');
const scannerPath = path.join(__dirname, '..', 'server', 'routes', 'scanner.js');

async function testTripCompletion() {
  const client = await pool.connect();
  try {
    console.log('🧪 Testing Dynamic Route Discovery Trip Completion...');

    // Get the current trip
    const tripResult = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        a.notes as assignment_notes,
        a.loading_location_id, a.unloading_location_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `);

    if (tripResult.rows.length === 0) {
      console.log('❌ No recent trips found');
      return;
    }

    const trip = tripResult.rows[0];
    console.log('📊 Current Trip State:');
    console.log(`  Trip ID: ${trip.id}`);
    console.log(`  Status: ${trip.status}`);
    console.log(`  Assignment Loading Location ID: ${trip.loading_location_id}`);
    console.log(`  Assignment Unloading Location ID: ${trip.unloading_location_id}`);
    console.log('');

    // Check if this is a dynamic assignment
    let isDynamicAssignment = false;
    try {
      const assignmentNotes = JSON.parse(trip.assignment_notes || '{}');
      isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
    } catch (error) {
      isDynamicAssignment = false;
    }

    console.log(`🔄 Dynamic Assignment: ${isDynamicAssignment ? 'Yes' : 'No'}`);

    if (trip.status !== 'unloading_end') {
      console.log(`❌ Trip status is ${trip.status}, expected unloading_end`);
      return;
    }

    // Simulate the validation logic from handleUnloadingEnd
    console.log('\n🔍 Testing Validation Logic:');
    
    if (isDynamicAssignment) {
      console.log('  Dynamic assignment validation:');
      if (!trip.unloading_start_time) {
        console.log('  ❌ Missing unloading start time');
        return;
      } else {
        console.log('  ✅ Unloading start time present');
      }
      
      console.log('  ✅ Dynamic assignment validation passed');
    } else {
      console.log('  Traditional assignment validation:');
      const missingSteps = [];
      if (!trip.loading_start_time) missingSteps.push("loading start");
      if (!trip.loading_end_time) missingSteps.push("loading end");
      if (!trip.unloading_start_time) missingSteps.push("unloading start");
      if (!trip.unloading_end_time) missingSteps.push("unloading end");
      
      if (missingSteps.length > 0) {
        console.log(`  ❌ Missing steps: ${missingSteps.join(", ")}`);
        return;
      } else {
        console.log('  ✅ All steps present');
      }
    }

    // Test trip completion logic
    console.log('\n🎯 Testing Trip Completion Logic:');
    
    // Get Point A location details
    const locationResult = await client.query(`
      SELECT id, name, type FROM locations WHERE id = $1
    `, [trip.loading_location_id]);

    if (locationResult.rows.length === 0) {
      console.log('❌ Loading location not found');
      return;
    }

    const location = locationResult.rows[0];
    console.log(`  Completion Location: ${location.name} (${location.type})`);

    if (location.type !== 'loading') {
      console.log('  ❌ Not a loading location');
      return;
    }

    console.log('  ✅ Valid loading location for trip completion');

    // Calculate duration
    let totalDuration;
    const now = new Date();
    
    if (isDynamicAssignment && trip.unloading_start_time) {
      totalDuration = Math.round(
        (now - new Date(trip.unloading_start_time)) / (1000 * 60)
      );
      console.log(`  📊 Duration calculation: ${totalDuration} minutes (from unloading start)`);
    } else if (trip.loading_start_time) {
      totalDuration = Math.round(
        (now - new Date(trip.loading_start_time)) / (1000 * 60)
      );
      console.log(`  📊 Duration calculation: ${totalDuration} minutes (from loading start)`);
    } else {
      totalDuration = 1;
      console.log(`  📊 Duration calculation: ${totalDuration} minute (fallback)`);
    }

    console.log('\n✅ Trip completion validation passed!');
    console.log('🎉 The trip should be able to complete successfully');
    
    // Show what the completion would look like
    console.log('\n📋 Expected Completion Result:');
    console.log(`  New Status: trip_completed`);
    console.log(`  Total Duration: ${totalDuration} minutes`);
    console.log(`  Completion Location: ${location.name}`);
    console.log(`  Completion Time: ${now.toISOString()}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testTripCompletion().catch(console.error);
