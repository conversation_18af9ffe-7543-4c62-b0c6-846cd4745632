const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'hauling_qr_system',
  password: 'PostgreSQLPassword',
  port: 5432,
});

async function checkCurrentDatabaseState() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking Current Database State...');

    // Get all recent trips
    const tripsResult = await client.query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.assignment_id,
        tl.loading_start_time, tl.loading_end_time,
        tl.unloading_start_time, tl.unloading_end_time,
        tl.trip_completed_time,
        tl.actual_loading_location_id, tl.actual_unloading_location_id,
        tl.created_at, tl.updated_at,
        a.assignment_code, a.notes as assignment_notes,
        a.loading_location_id, a.unloading_location_id,
        ll.name as assigned_loading_location,
        ul.name as assigned_unloading_location,
        all_loc.name as actual_loading_location,
        aul_loc.name as actual_unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 10
    `);

    console.log(`\n📊 Found ${tripsResult.rows.length} recent trips:`);
    console.log('=' .repeat(80));

    tripsResult.rows.forEach((trip, index) => {
      console.log(`\n🚚 Trip #${trip.trip_number} (ID: ${trip.id})`);
      console.log(`   Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
      console.log(`   Status: ${trip.status}`);
      console.log(`   Route: ${trip.assigned_loading_location} → ${trip.assigned_unloading_location}`);
      console.log(`   Actual: ${trip.actual_loading_location || 'None'} → ${trip.actual_unloading_location || 'None'}`);
      console.log(`   Created: ${trip.created_at}`);
      console.log(`   Updated: ${trip.updated_at}`);
      
      // Check timestamps
      const hasLoading = !!(trip.loading_start_time && trip.loading_end_time);
      const hasUnloading = !!(trip.unloading_start_time && trip.unloading_end_time);
      console.log(`   Loading: ${hasLoading ? 'Complete ✅' : 'Incomplete ❌'}`);
      console.log(`   Unloading: ${hasUnloading ? 'Complete ✅' : 'Incomplete ❌'}`);
      
      // Check assignment type
      let isDynamicAssignment = false;
      let assignmentCreationMethod = 'unknown';
      if (trip.assignment_notes) {
        try {
          const assignmentNotes = JSON.parse(trip.assignment_notes);
          assignmentCreationMethod = assignmentNotes.creation_method || 'unknown';
          isDynamicAssignment = assignmentCreationMethod === 'dynamic_assignment';
        } catch (e) {
          assignmentCreationMethod = 'parse_error';
        }
      }
      console.log(`   Assignment Type: ${assignmentCreationMethod}`);
      console.log(`   Dynamic: ${isDynamicAssignment ? 'Yes' : 'No'}`);
      
      console.log('   ' + '-'.repeat(70));
    });

    // Find trips with unloading_end status
    const unloadingEndTrips = tripsResult.rows.filter(t => t.status === 'unloading_end');
    if (unloadingEndTrips.length > 0) {
      console.log('\n⚠️ TRIPS WITH UNLOADING_END STATUS:');
      console.log('=' .repeat(50));
      
      unloadingEndTrips.forEach(trip => {
        console.log(`\n🎯 Trip #${trip.trip_number} (ID: ${trip.id})`);
        console.log(`   Status: ${trip.status}`);
        console.log(`   Assignment: ${trip.assignment_code}`);
        
        // Check if this should be able to complete
        const hasLoadingPhase = !!(trip.loading_start_time && trip.loading_end_time);
        const hasUnloadingPhase = !!(trip.unloading_start_time && trip.unloading_end_time);
        
        let isDynamicAssignment = false;
        if (trip.assignment_notes) {
          try {
            const assignmentNotes = JSON.parse(trip.assignment_notes);
            isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
          } catch (e) {
            // Ignore
          }
        }
        
        console.log(`   Has Loading Phase: ${hasLoadingPhase ? 'Yes' : 'No'}`);
        console.log(`   Has Unloading Phase: ${hasUnloadingPhase ? 'Yes' : 'No'}`);
        console.log(`   Is Dynamic Assignment: ${isDynamicAssignment ? 'Yes' : 'No'}`);
        
        // Determine if it should be able to complete
        let canComplete = false;
        let reason = '';
        
        if (isDynamicAssignment) {
          if (hasUnloadingPhase) {
            canComplete = true;
            reason = 'Dynamic assignment with unloading phase complete';
          } else {
            canComplete = false;
            reason = 'Dynamic assignment missing unloading phase';
          }
        } else {
          if (hasLoadingPhase && hasUnloadingPhase) {
            canComplete = true;
            reason = 'Traditional assignment with all phases complete';
          } else {
            canComplete = false;
            reason = 'Traditional assignment missing required phases';
          }
        }
        
        console.log(`   Can Complete: ${canComplete ? 'YES ✅' : 'NO ❌'}`);
        console.log(`   Reason: ${reason}`);
        
        if (!canComplete && trip.status === 'unloading_end') {
          console.log(`   ⚠️ ISSUE: Trip has unloading_end status but cannot complete!`);
        }
      });
    }

    // Check for any trips that should be completable
    console.log('\n🎯 COMPLETABLE TRIPS ANALYSIS:');
    console.log('=' .repeat(40));
    
    const completableTrips = tripsResult.rows.filter(trip => {
      if (trip.status !== 'unloading_end') return false;
      
      const hasUnloadingPhase = !!(trip.unloading_start_time && trip.unloading_end_time);
      let isDynamicAssignment = false;
      
      if (trip.assignment_notes) {
        try {
          const assignmentNotes = JSON.parse(trip.assignment_notes);
          isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
        } catch (e) {
          // Ignore
        }
      }
      
      if (isDynamicAssignment && hasUnloadingPhase) {
        return true; // Dynamic assignment with unloading complete
      }
      
      const hasLoadingPhase = !!(trip.loading_start_time && trip.loading_end_time);
      if (!isDynamicAssignment && hasLoadingPhase && hasUnloadingPhase) {
        return true; // Traditional assignment with all phases complete
      }
      
      return false;
    });
    
    if (completableTrips.length > 0) {
      console.log(`Found ${completableTrips.length} trips that should be completable:`);
      completableTrips.forEach(trip => {
        console.log(`   - Trip #${trip.trip_number} (ID: ${trip.id})`);
      });
    } else {
      console.log('No trips found that should be completable');
    }

  } catch (error) {
    console.error('❌ Error checking database state:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

checkCurrentDatabaseState().catch(console.error);
