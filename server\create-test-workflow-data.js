const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
});

async function createTestWorkflowData() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Creating Multi-Location Workflow Test Data...\n');
    
    // 1. Check current state
    const currentTrips = await client.query(`
      SELECT id, status, location_sequence IS NOT NULL as has_seq 
      FROM trip_logs 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log('📊 Current trips:');
    currentTrips.rows.forEach(row => {
      console.log(`  Trip ${row.id}: ${row.status} | Has sequence: ${row.has_seq}`);
    });
    
    // 2. Get an assignment to work with
    const assignment = await client.query(`
      SELECT a.*, ll.name as loading_name, ul.name as unloading_name
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      ORDER BY a.created_at DESC
      LIMIT 1
    `);
    
    if (assignment.rows.length === 0) {
      console.log('❌ No assignments found');
      return;
    }
    
    const assignmentData = assignment.rows[0];
    console.log(`\n📋 Using assignment ${assignmentData.id}: ${assignmentData.loading_name} → ${assignmentData.unloading_name}`);
    
    // 3. Get next trip number for this assignment
    const nextTripResult = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs
      WHERE assignment_id = $1
    `, [assignmentData.id]);

    const nextTripNumber = nextTripResult.rows[0].next_trip_number;
    console.log(`📊 Using trip number ${nextTripNumber} for standard trip`);

    // 4. Create a completed standard trip with location sequence
    const standardTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time,
        trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        is_extended_trip, workflow_type,
        location_sequence,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      RETURNING *
    `, [
      assignmentData.id,
      nextTripNumber, // trip_number
      'trip_completed',
      new Date(Date.now() - 7200000), // 2 hours ago
      new Date(Date.now() - 6900000), // 1h 55m ago
      new Date(Date.now() - 5400000), // 1h 30m ago
      new Date(Date.now() - 5100000), // 1h 25m ago
      new Date(Date.now() - 4800000), // 1h 20m ago
      assignmentData.loading_location_id,
      assignmentData.unloading_location_id,
      false, // is_extended_trip
      'standard', // workflow_type
      JSON.stringify([
        {
          name: assignmentData.loading_name,
          type: 'loading',
          confirmed: true,
          location_id: assignmentData.loading_location_id
        },
        {
          name: assignmentData.unloading_name,
          type: 'unloading',
          confirmed: true,
          location_id: assignmentData.unloading_location_id
        }
      ]),
      new Date(Date.now() - 7200000),
      new Date()
    ]);
    
    console.log(`✅ Created standard trip ${standardTrip.rows[0].id} with location sequence`);
    
    // 4. Get another location for extended trip
    const altLocation = await client.query(`
      SELECT * FROM locations 
      WHERE type = 'loading' 
        AND id != $1 
      LIMIT 1
    `, [assignmentData.loading_location_id]);
    
    if (altLocation.rows.length > 0) {
      const extensionLocation = altLocation.rows[0];
      
      // 5. Create extended trip assignment
      const extAssignment = await client.query(`
        INSERT INTO assignments (
          truck_id, driver_id, loading_location_id, unloading_location_id,
          assigned_date, created_at, updated_at, notes
        )
        VALUES ($1, $2, $3, $4, CURRENT_DATE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $5)
        ON CONFLICT DO NOTHING
        RETURNING *
      `, [
        assignmentData.truck_id,
        assignmentData.driver_id,
        extensionLocation.id,
        assignmentData.unloading_location_id,
        JSON.stringify({ creation_method: 'test_extended_trip', baseline_trip_id: standardTrip.rows[0].id })
      ]);
      
      let extAssignmentId;
      if (extAssignment.rows.length > 0) {
        extAssignmentId = extAssignment.rows[0].id;
        console.log(`✅ Created extended assignment ${extAssignmentId}`);
      } else {
        // Get existing assignment
        const existing = await client.query(`
          SELECT id FROM assignments 
          WHERE truck_id = $1 AND driver_id = $2 
            AND loading_location_id = $3 AND unloading_location_id = $4
          LIMIT 1
        `, [
          assignmentData.truck_id,
          assignmentData.driver_id,
          extensionLocation.id,
          assignmentData.unloading_location_id
        ]);
        extAssignmentId = existing.rows[0]?.id;
      }
      
      if (extAssignmentId) {
        // 6. Create extended trip
        const extendedTrip = await client.query(`
          INSERT INTO trip_logs (
            assignment_id, trip_number, status,
            loading_start_time, loading_end_time,
            unloading_start_time, unloading_end_time,
            trip_completed_time,
            actual_loading_location_id, actual_unloading_location_id,
            is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
            location_sequence,
            created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
          RETURNING *
        `, [
          extAssignmentId,
          1, // trip_number
          'trip_completed',
          new Date(Date.now() - 3600000), // 1 hour ago
          new Date(Date.now() - 3300000), // 55 minutes ago
          new Date(Date.now() - 1800000), // 30 minutes ago
          new Date(Date.now() - 1500000), // 25 minutes ago
          new Date(Date.now() - 1200000), // 20 minutes ago
          extensionLocation.id,
          assignmentData.unloading_location_id,
          true, // is_extended_trip
          'extended', // workflow_type
          standardTrip.rows[0].id, // baseline_trip_id
          1, // cycle_number
          JSON.stringify([
            {
              name: extensionLocation.name,
              type: 'loading',
              confirmed: true,
              location_id: extensionLocation.id
            },
            {
              name: assignmentData.unloading_name,
              type: 'unloading',
              confirmed: true,
              location_id: assignmentData.unloading_location_id
            }
          ]),
          new Date(Date.now() - 3600000),
          new Date()
        ]);
        
        console.log(`✅ Created extended trip ${extendedTrip.rows[0].id} (A→B→C extension)`);
        
        // 7. Create cycle trip
        const cycleTrip = await client.query(`
          INSERT INTO trip_logs (
            assignment_id, trip_number, status,
            loading_start_time, loading_end_time,
            unloading_start_time, unloading_end_time,
            trip_completed_time,
            actual_loading_location_id, actual_unloading_location_id,
            is_extended_trip, workflow_type, baseline_trip_id, cycle_number,
            location_sequence,
            created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
          RETURNING *
        `, [
          extAssignmentId,
          2, // trip_number
          'trip_completed',
          new Date(Date.now() - 900000), // 15 minutes ago
          new Date(Date.now() - 600000), // 10 minutes ago
          new Date(Date.now() - 300000), // 5 minutes ago
          new Date(Date.now() - 180000), // 3 minutes ago
          new Date(Date.now() - 60000),  // 1 minute ago
          extensionLocation.id,
          assignmentData.unloading_location_id,
          true, // is_extended_trip
          'cycle', // workflow_type
          standardTrip.rows[0].id, // baseline_trip_id
          2, // cycle_number
          JSON.stringify([
            {
              name: extensionLocation.name,
              type: 'loading',
              confirmed: true,
              location_id: extensionLocation.id
            },
            {
              name: assignmentData.unloading_name,
              type: 'unloading',
              confirmed: true,
              location_id: assignmentData.unloading_location_id
            }
          ]),
          new Date(Date.now() - 900000),
          new Date()
        ]);
        
        console.log(`✅ Created cycle trip ${cycleTrip.rows[0].id} (C→B→C cycle)`);
      }
    }
    
    // 8. Verify the created data
    console.log('\n📊 Final verification:');
    const finalCheck = await client.query(`
      SELECT id, status, is_extended_trip, workflow_type, cycle_number,
             jsonb_array_length(location_sequence) as seq_length
      FROM trip_logs 
      WHERE location_sequence IS NOT NULL
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    finalCheck.rows.forEach(row => {
      const workflowLabel = row.is_extended_trip 
        ? `${row.workflow_type}${row.cycle_number ? ` #${row.cycle_number}` : ''}`
        : 'standard';
      console.log(`  Trip ${row.id}: ${row.status} | ${workflowLabel} | ${row.seq_length} locations`);
    });
    
    console.log('\n🎉 Multi-location workflow test data created successfully!');
    console.log('   You should now see enhanced routes with 📍 confirmed indicators');
    console.log('   Extended trips will show 🔄 workflow indicators');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

createTestWorkflowData();
